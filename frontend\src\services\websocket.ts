import { io, Socket } from 'socket.io-client';

export interface ConnectionStatus {
  connectionId: string;
  status: 'connected' | 'disconnected' | 'error';
  error?: string;
  timestamp: string;
}

export interface RealtimeStats {
  connectionId: string;
  stats: {
    connected_clients: string;
    used_memory_human: string;
    instantaneous_ops_per_sec: string;
    keyspace_hits: string;
    keyspace_misses: string;
  };
  timestamp: string;
}

export interface ConnectionInfo {
  connectionId: string;
  info: {
    name: string;
    host: string;
    port: number;
    database: number;
  };
  timestamp: string;
}

type EventCallback<T = any> = (data: T) => void;

class WebSocketService {
  private socket: Socket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private eventListeners: Map<string, EventCallback[]> = new Map();

  constructor() {
    this.connect();
  }

  private connect() {
    if (this.socket?.connected) {
      return;
    }

    const serverUrl = import.meta.env.VITE_API_URL || 'http://localhost:3001';
    
    this.socket = io(serverUrl, {
      transports: ['websocket', 'polling'],
      timeout: 10000,
      forceNew: true,
    });

    this.setupEventHandlers();
  }

  private setupEventHandlers() {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('WebSocket connected:', this.socket?.id);
      this.reconnectAttempts = 0;
      this.emit('connected', { clientId: this.socket?.id });
    });

    this.socket.on('disconnect', (reason) => {
      console.log('WebSocket disconnected:', reason);
      this.emit('disconnected', { reason });
      
      // 自动重连
      if (reason === 'io server disconnect') {
        // 服务器主动断开，不重连
        return;
      }
      
      this.scheduleReconnect();
    });

    this.socket.on('connect_error', (error) => {
      console.error('WebSocket connection error:', error);
      this.emit('error', { error: error.message });
      this.scheduleReconnect();
    });

    // 监听服务器事件
    this.socket.on('connected', (data) => {
      this.emit('server-connected', data);
    });

    this.socket.on('connection-status', (data: ConnectionStatus) => {
      this.emit('connection-status', data);
    });

    this.socket.on('realtime-stats', (data: RealtimeStats) => {
      this.emit('realtime-stats', data);
    });

    this.socket.on('connection-info', (data: ConnectionInfo) => {
      this.emit('connection-info', data);
    });

    this.socket.on('error', (data) => {
      this.emit('server-error', data);
    });
  }

  private scheduleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached');
      this.emit('max-reconnect-attempts', {});
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
    
    console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`);
    
    setTimeout(() => {
      this.connect();
    }, delay);
  }

  // 订阅连接状态
  public subscribeToConnection(connectionId: string) {
    if (!this.socket?.connected) {
      console.warn('WebSocket not connected, cannot subscribe to connection');
      return;
    }

    this.socket.emit('subscribe-connection', connectionId);
    console.log(`Subscribed to connection: ${connectionId}`);
  }

  // 取消订阅连接状态
  public unsubscribeFromConnection() {
    if (!this.socket?.connected) {
      return;
    }

    this.socket.emit('unsubscribe-connection');
    console.log('Unsubscribed from connection');
  }

  // 请求实时数据
  public requestRealtimeData(connectionId: string, type: 'stats' | 'info') {
    if (!this.socket?.connected) {
      console.warn('WebSocket not connected, cannot request realtime data');
      return;
    }

    this.socket.emit('request-realtime-data', { connectionId, type });
  }

  // 事件监听
  public on<T = any>(event: string, callback: EventCallback<T>) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  // 移除事件监听
  public off(event: string, callback?: EventCallback) {
    const listeners = this.eventListeners.get(event);
    if (!listeners) return;

    if (callback) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    } else {
      this.eventListeners.set(event, []);
    }
  }

  // 触发事件
  private emit<T = any>(event: string, data: T) {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in WebSocket event listener for ${event}:`, error);
        }
      });
    }
  }

  // 获取连接状态
  public isConnected(): boolean {
    return this.socket?.connected || false;
  }

  // 获取客户端ID
  public getClientId(): string | undefined {
    return this.socket?.id;
  }

  // 手动重连
  public reconnect() {
    if (this.socket) {
      this.socket.disconnect();
    }
    this.reconnectAttempts = 0;
    this.connect();
  }

  // 断开连接
  public disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.eventListeners.clear();
  }

  // 发送自定义消息
  public send(event: string, data?: any) {
    if (!this.socket?.connected) {
      console.warn(`WebSocket not connected, cannot send ${event}`);
      return;
    }

    this.socket.emit(event, data);
  }
}

// 创建单例实例
const websocketService = new WebSocketService();

export default websocketService;
