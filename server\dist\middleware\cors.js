"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.handleCorsError = exports.corsMiddleware = exports.corsOptions = void 0;
const cors_1 = __importDefault(require("cors"));
exports.corsOptions = {
    origin: (origin, callback) => {
        const allowedOrigins = (process.env.CORS_ORIGIN || 'http://localhost:5173').split(',');
        if (!origin)
            return callback(null, true);
        if (allowedOrigins.includes(origin)) {
            callback(null, true);
        }
        else {
            callback(new Error('Not allowed by CORS'));
        }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    exposedHeaders: ['X-Total-Count', 'X-Connection-Id']
};
exports.corsMiddleware = (0, cors_1.default)(exports.corsOptions);
const handleCorsError = (err, req, res, next) => {
    if (err.message === 'Not allowed by CORS') {
        res.status(403).json({
            success: false,
            error: 'CORS policy violation',
            message: 'Origin not allowed'
        });
    }
    else {
        next(err);
    }
};
exports.handleCorsError = handleCorsError;
//# sourceMappingURL=cors.js.map