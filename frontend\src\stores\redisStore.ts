import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { ApiService, RedisKeyInfo, RedisKeyValue, DatabaseInfo } from '../services/api';

interface RedisState {
  // 状态
  databases: DatabaseInfo[];
  currentDatabase: number;
  keys: RedisKeyInfo[];
  selectedKey: string | null;
  keyValue: RedisKeyValue | null;
  keysCursor: string;
  hasMoreKeys: boolean;
  searchPattern: string;
  loading: boolean;
  error: string | null;

  // 操作
  fetchDatabases: (connectionId: string) => Promise<void>;
  setCurrentDatabase: (db: number) => void;
  fetchKeys: (connectionId: string, reset?: boolean) => Promise<void>;
  loadMoreKeys: (connectionId: string) => Promise<void>;
  setSearchPattern: (pattern: string) => void;
  selectKey: (key: string | null) => void;
  fetchKeyValue: (connectionId: string, key: string) => Promise<void>;
  executeCommand: (connectionId: string, command: string, args?: string[]) => Promise<any>;
  clearError: () => void;
  reset: () => void;
}

export const useRedisStore = create<RedisState>()(
  devtools(
    (set, get) => ({
      // 初始状态
      databases: [],
      currentDatabase: 0,
      keys: [],
      selectedKey: null,
      keyValue: null,
      keysCursor: '0',
      hasMoreKeys: false,
      searchPattern: '*',
      loading: false,
      error: null,

      // 获取数据库列表
      fetchDatabases: async (connectionId: string) => {
        set({ loading: true, error: null });
        try {
          const response = await ApiService.getDatabases(connectionId);
          if (response.success && response.data) {
            set({ databases: response.data, loading: false });
          } else {
            set({ error: response.error || 'Failed to fetch databases', loading: false });
          }
        } catch (error: any) {
          set({ 
            error: error.response?.data?.error || error.message || 'Failed to fetch databases',
            loading: false 
          });
        }
      },

      // 设置当前数据库
      setCurrentDatabase: (db: number) => {
        set({ currentDatabase: db, keys: [], selectedKey: null, keyValue: null, keysCursor: '0' });
      },

      // 获取键列表
      fetchKeys: async (connectionId: string, reset: boolean = true) => {
        const { currentDatabase, searchPattern } = get();
        set({ loading: true, error: null });
        
        try {
          const response = await ApiService.getKeys(connectionId, {
            pattern: searchPattern,
            cursor: reset ? '0' : get().keysCursor,
            count: 100,
            db: currentDatabase,
          });

          if (response.success && response.data) {
            const { cursor, keys, hasMore } = response.data;
            set({
              keys: reset ? keys : [...get().keys, ...keys],
              keysCursor: cursor,
              hasMoreKeys: hasMore,
              loading: false,
            });
          } else {
            set({ error: response.error || 'Failed to fetch keys', loading: false });
          }
        } catch (error: any) {
          set({ 
            error: error.response?.data?.error || error.message || 'Failed to fetch keys',
            loading: false 
          });
        }
      },

      // 加载更多键
      loadMoreKeys: async (connectionId: string) => {
        const { hasMoreKeys, loading } = get();
        if (!hasMoreKeys || loading) return;
        
        await get().fetchKeys(connectionId, false);
      },

      // 设置搜索模式
      setSearchPattern: (pattern: string) => {
        set({ searchPattern: pattern });
      },

      // 选择键
      selectKey: (key: string | null) => {
        set({ selectedKey: key, keyValue: null });
      },

      // 获取键值
      fetchKeyValue: async (connectionId: string, key: string) => {
        const { currentDatabase } = get();
        set({ loading: true, error: null });
        
        try {
          const response = await ApiService.getKeyValue(connectionId, key, currentDatabase);
          if (response.success && response.data) {
            set({ keyValue: response.data, loading: false });
          } else {
            set({ error: response.error || 'Failed to fetch key value', loading: false });
          }
        } catch (error: any) {
          set({ 
            error: error.response?.data?.error || error.message || 'Failed to fetch key value',
            loading: false 
          });
        }
      },

      // 执行Redis命令
      executeCommand: async (connectionId: string, command: string, args: string[] = []) => {
        const { currentDatabase } = get();
        set({ loading: true, error: null });
        
        try {
          const response = await ApiService.executeCommand(connectionId, command, args, currentDatabase);
          set({ loading: false });
          
          if (response.success && response.data) {
            return response.data.result;
          } else {
            set({ error: response.error || 'Command execution failed' });
            throw new Error(response.error || 'Command execution failed');
          }
        } catch (error: any) {
          set({ 
            error: error.response?.data?.error || error.message || 'Command execution failed',
            loading: false 
          });
          throw error;
        }
      },

      // 清除错误
      clearError: () => {
        set({ error: null });
      },

      // 重置状态
      reset: () => {
        set({
          databases: [],
          currentDatabase: 0,
          keys: [],
          selectedKey: null,
          keyValue: null,
          keysCursor: '0',
          hasMoreKeys: false,
          searchPattern: '*',
          loading: false,
          error: null,
        });
      },
    }),
    {
      name: 'redis-store',
    }
  )
);
