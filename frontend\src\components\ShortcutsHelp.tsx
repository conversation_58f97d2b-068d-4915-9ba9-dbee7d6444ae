import React from 'react';
import { Modal, Typography, Space, Divider, Tag, Row, Col } from 'antd';
import { KeyOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;

interface ShortcutItem {
  key: string;
  description: string;
  category?: string;
}

interface ShortcutsHelpProps {
  visible: boolean;
  onClose: () => void;
}

const SHORTCUTS: ShortcutItem[] = [
  // 应用操作
  { key: 'Ctrl + T', description: '切换主题', category: '应用操作' },
  { key: 'Shift + ?', description: '显示帮助', category: '应用操作' },
  { key: 'Ctrl + K', description: '显示快捷键', category: '应用操作' },
  
  // 连接管理
  { key: 'Ctrl + N', description: '新建连接', category: '连接管理' },
  { key: 'Enter', description: '连接到Redis', category: '连接管理' },
  { key: 'Ctrl + D', description: '断开连接', category: '连接管理' },
  
  // 数据操作
  { key: 'Ctrl + R', description: '刷新数据', category: '数据操作' },
  { key: 'Delete', description: '删除选中的键', category: '数据操作' },
  { key: 'Ctrl + F', description: '搜索', category: '数据操作' },
  
  // 导航
  { key: 'Ctrl + Tab', description: '下一个标签页', category: '导航' },
  { key: 'Ctrl + Shift + Tab', description: '上一个标签页', category: '导航' },
  
  // 命令执行
  { key: 'Ctrl + Enter', description: '执行命令', category: '命令执行' },
  { key: 'Ctrl + L', description: '清空控制台', category: '命令执行' },
  
  // 数据导入导出
  { key: 'Ctrl + E', description: '导出数据', category: '数据导入导出' },
  { key: 'Ctrl + I', description: '导入数据', category: '数据导入导出' },
];

const ShortcutsHelp: React.FC<ShortcutsHelpProps> = ({ visible, onClose }) => {
  // 按类别分组快捷键
  const groupedShortcuts = SHORTCUTS.reduce((groups, shortcut) => {
    const category = shortcut.category || '其他';
    if (!groups[category]) {
      groups[category] = [];
    }
    groups[category].push(shortcut);
    return groups;
  }, {} as Record<string, ShortcutItem[]>);

  const renderShortcutKey = (key: string) => {
    const keys = key.split(' + ');
    return (
      <Space size={4}>
        {keys.map((k, index) => (
          <React.Fragment key={k}>
            <Tag className="font-mono text-xs px-2 py-1 bg-gray-100 border border-gray-300 rounded">
              {k}
            </Tag>
            {index < keys.length - 1 && <span className="text-gray-400">+</span>}
          </React.Fragment>
        ))}
      </Space>
    );
  };

  return (
    <Modal
      title={
        <Space>
          <KeyOutlined />
          <span>键盘快捷键</span>
        </Space>
      }
      open={visible}
      onCancel={onClose}
      footer={null}
      width={800}
      className="shortcuts-help-modal"
    >
      <div className="max-h-96 overflow-y-auto">
        {Object.entries(groupedShortcuts).map(([category, shortcuts]) => (
          <div key={category} className="mb-6">
            <Title level={5} className="mb-3 text-gray-700">
              {category}
            </Title>
            
            <div className="space-y-2">
              {shortcuts.map((shortcut) => (
                <Row key={shortcut.key} align="middle" className="py-2">
                  <Col span={8}>
                    {renderShortcutKey(shortcut.key)}
                  </Col>
                  <Col span={16}>
                    <Text className="text-gray-600">{shortcut.description}</Text>
                  </Col>
                </Row>
              ))}
            </div>
            
            {Object.keys(groupedShortcuts).indexOf(category) < Object.keys(groupedShortcuts).length - 1 && (
              <Divider className="my-4" />
            )}
          </div>
        ))}
      </div>
      
      <Divider />
      
      <div className="text-center">
        <Text type="secondary" className="text-sm">
          提示：在输入框中时快捷键不会生效
        </Text>
      </div>
    </Modal>
  );
};

export default ShortcutsHelp;
