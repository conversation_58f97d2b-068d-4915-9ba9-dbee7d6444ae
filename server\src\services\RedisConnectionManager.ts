import Redis, { RedisOptions } from 'ioredis';
import { EventEmitter } from 'events';

export interface RedisConnectionConfig {
  id: string;
  name: string;
  host: string;
  port: number;
  password?: string;
  database: number;
  ssl: boolean;
  timeout: number;
  retryDelayOnFailover: number;
  maxRetriesPerRequest: number;
  lazyConnect: boolean;
}

export enum ConnectionStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  ERROR = 'error',
  RECONNECTING = 'reconnecting'
}

export interface ConnectionInfo {
  id: string;
  config: RedisConnectionConfig;
  client: Redis;
  status: ConnectionStatus;
  lastError?: string;
  connectedAt?: Date;
  lastActivity?: Date;
}

export class RedisConnectionManager extends EventEmitter {
  private connections: Map<string, ConnectionInfo> = new Map();
  private connectionTimeouts: Map<string, NodeJS.Timeout> = new Map();
  
  constructor() {
    super();
    this.setMaxListeners(100); // 增加最大监听器数量
  }

  /**
   * 创建Redis连接
   */
  async createConnection(config: RedisConnectionConfig): Promise<string> {
    try {
      // 检查是否已存在相同ID的连接
      if (this.connections.has(config.id)) {
        await this.removeConnection(config.id);
      }

      const redisOptions: RedisOptions = {
        host: config.host,
        port: config.port,
        password: config.password,
        db: config.database,
        connectTimeout: config.timeout,
        lazyConnect: config.lazyConnect,
        maxRetriesPerRequest: config.maxRetriesPerRequest,
        enableReadyCheck: true,
      };

      // SSL配置
      if (config.ssl) {
        redisOptions.tls = {};
      }

      const client = new Redis(redisOptions);
      
      // 创建连接信息
      const connectionInfo: ConnectionInfo = {
        id: config.id,
        config,
        client,
        status: ConnectionStatus.CONNECTING,
      };

      this.connections.set(config.id, connectionInfo);
      this.setupClientEventHandlers(connectionInfo);

      // 如果不是懒连接，立即连接
      if (!config.lazyConnect) {
        await client.connect();
      }

      this.emit('connectionCreated', config.id);
      return config.id;
    } catch (error) {
      this.emit('connectionError', config.id, error);
      throw error;
    }
  }

  /**
   * 设置客户端事件处理器
   */
  private setupClientEventHandlers(connectionInfo: ConnectionInfo) {
    const { id, client } = connectionInfo;

    client.on('connect', () => {
      this.updateConnectionStatus(id, ConnectionStatus.CONNECTING);
      this.emit('connectionStatusChanged', id, ConnectionStatus.CONNECTING);
    });

    client.on('ready', () => {
      const info = this.connections.get(id);
      if (info) {
        info.connectedAt = new Date();
        info.lastActivity = new Date();
        this.updateConnectionStatus(id, ConnectionStatus.CONNECTED);
        this.emit('connectionStatusChanged', id, ConnectionStatus.CONNECTED);
      }
    });

    client.on('error', (error) => {
      const info = this.connections.get(id);
      if (info) {
        info.lastError = error.message;
        this.updateConnectionStatus(id, ConnectionStatus.ERROR);
        this.emit('connectionError', id, error);
        this.emit('connectionStatusChanged', id, ConnectionStatus.ERROR);
      }
    });

    client.on('close', () => {
      this.updateConnectionStatus(id, ConnectionStatus.DISCONNECTED);
      this.emit('connectionStatusChanged', id, ConnectionStatus.DISCONNECTED);
    });

    client.on('reconnecting', () => {
      this.updateConnectionStatus(id, ConnectionStatus.RECONNECTING);
      this.emit('connectionStatusChanged', id, ConnectionStatus.RECONNECTING);
    });

    client.on('end', () => {
      this.updateConnectionStatus(id, ConnectionStatus.DISCONNECTED);
      this.emit('connectionStatusChanged', id, ConnectionStatus.DISCONNECTED);
    });
  }

  /**
   * 更新连接状态
   */
  private updateConnectionStatus(id: string, status: ConnectionStatus) {
    const connectionInfo = this.connections.get(id);
    if (connectionInfo) {
      connectionInfo.status = status;
      connectionInfo.lastActivity = new Date();
    }
  }

  /**
   * 获取连接客户端
   */
  getConnection(id: string): Redis | null {
    const connectionInfo = this.connections.get(id);
    return connectionInfo?.client || null;
  }

  /**
   * 获取连接信息
   */
  getConnectionInfo(id: string): ConnectionInfo | null {
    return this.connections.get(id) || null;
  }

  /**
   * 获取所有连接
   */
  getAllConnections(): ConnectionInfo[] {
    return Array.from(this.connections.values());
  }

  /**
   * 获取活跃连接
   */
  getActiveConnections(): ConnectionInfo[] {
    return this.getAllConnections().filter(
      conn => conn.status === ConnectionStatus.CONNECTED
    );
  }

  /**
   * 测试连接
   */
  async testConnection(config: RedisConnectionConfig): Promise<boolean> {
    const testClient = new Redis({
      host: config.host,
      port: config.port,
      password: config.password,
      db: config.database,
      connectTimeout: config.timeout,
      lazyConnect: true,
      maxRetriesPerRequest: 1,
      ...(config.ssl && { tls: {} }),
    });

    try {
      await testClient.connect();
      await testClient.ping();
      await testClient.disconnect();
      return true;
    } catch (error) {
      try {
        await testClient.disconnect();
      } catch (disconnectError) {
        // 忽略断开连接时的错误
      }
      return false;
    }
  }

  /**
   * 移除连接
   */
  async removeConnection(id: string): Promise<void> {
    const connectionInfo = this.connections.get(id);
    if (!connectionInfo) {
      return;
    }

    try {
      // 清除超时定时器
      const timeout = this.connectionTimeouts.get(id);
      if (timeout) {
        clearTimeout(timeout);
        this.connectionTimeouts.delete(id);
      }

      // 断开连接
      if (connectionInfo.client.status !== 'end') {
        await connectionInfo.client.disconnect();
      }

      // 移除连接信息
      this.connections.delete(id);
      this.emit('connectionRemoved', id);
    } catch (error) {
      console.error(`Error removing connection ${id}:`, error);
      // 即使出错也要移除连接信息
      this.connections.delete(id);
      this.emit('connectionRemoved', id);
    }
  }

  /**
   * 关闭所有连接
   */
  async closeAllConnections(): Promise<void> {
    const connectionIds = Array.from(this.connections.keys());
    await Promise.all(
      connectionIds.map(id => this.removeConnection(id))
    );
  }

  /**
   * 获取连接统计信息
   */
  getConnectionStats() {
    const connections = this.getAllConnections();
    const stats = {
      total: connections.length,
      connected: 0,
      connecting: 0,
      disconnected: 0,
      error: 0,
      reconnecting: 0,
    };

    connections.forEach(conn => {
      switch (conn.status) {
        case ConnectionStatus.CONNECTED:
          stats.connected++;
          break;
        case ConnectionStatus.CONNECTING:
          stats.connecting++;
          break;
        case ConnectionStatus.DISCONNECTED:
          stats.disconnected++;
          break;
        case ConnectionStatus.ERROR:
          stats.error++;
          break;
        case ConnectionStatus.RECONNECTING:
          stats.reconnecting++;
          break;
      }
    });

    return stats;
  }
}

// 单例实例
export const connectionManager = new RedisConnectionManager();
