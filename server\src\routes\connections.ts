import { Router, Request, Response } from 'express';
import { connectionManager, RedisConnectionConfig } from '../services/RedisConnectionManager';
import { asyncHandler } from '../utils/asyncHandler';
import { ApiResponse } from '../types/api';

const router = Router();

// 获取所有连接
router.get('/connections', asyncHandler(async (req: Request, res: Response) => {
  const connections = connectionManager.getAllConnections();
  
  const response: ApiResponse = {
    success: true,
    data: connections.map(conn => ({
      id: conn.id,
      name: conn.config.name,
      host: conn.config.host,
      port: conn.config.port,
      database: conn.config.database,
      ssl: conn.config.ssl,
      status: conn.status,
      connectedAt: conn.connectedAt,
      lastActivity: conn.lastActivity,
      lastError: conn.lastError,
    })),
    message: 'Connections retrieved successfully'
  };
  
  return res.json(response);
}));

// 创建新连接
router.post('/connections', asyncHandler(async (req: Request, res: Response) => {
  const config: RedisConnectionConfig = req.body;
  
  // 基础验证
  if (!config.name || !config.host || !config.port) {
    return res.status(400).json({
      success: false,
      error: 'Missing required fields: name, host, port',
    });
  }

  // 生成连接ID
  if (!config.id) {
    config.id = `conn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // 设置默认值
  config.database = config.database || 0;
  config.ssl = config.ssl || false;
  config.timeout = config.timeout || 5000;
  config.retryDelayOnFailover = config.retryDelayOnFailover || 100;
  config.maxRetriesPerRequest = config.maxRetriesPerRequest || 3;
  config.lazyConnect = config.lazyConnect !== false; // 默认为true

  try {
    const connectionId = await connectionManager.createConnection(config);

    const response: ApiResponse = {
      success: true,
      data: { connectionId },
      message: 'Connection created successfully'
    };

    return res.status(201).json(response);
  } catch (error: any) {
    const response: ApiResponse = {
      success: false,
      error: error.message,
      message: 'Failed to create connection'
    };

    return res.status(500).json(response);
  }
}));

// 测试连接
router.post('/connections/test', asyncHandler(async (req: Request, res: Response) => {
  const config: RedisConnectionConfig = req.body;
  
  if (!config.host || !config.port) {
    return res.status(400).json({
      success: false,
      error: 'Missing required fields: host, port',
    });
  }

  try {
    const isConnectable = await connectionManager.testConnection(config);

    const response: ApiResponse = {
      success: true,
      data: { connectable: isConnectable },
      message: isConnectable ? 'Connection test successful' : 'Connection test failed'
    };

    return res.json(response);
  } catch (error: any) {
    const response: ApiResponse = {
      success: false,
      error: error.message,
      message: 'Connection test failed'
    };

    return res.status(500).json(response);
  }
}));

// 获取单个连接信息
router.get('/connections/:id', asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const connectionInfo = connectionManager.getConnectionInfo(id);
  
  if (!connectionInfo) {
    return res.status(404).json({
      success: false,
      error: 'Connection not found',
    });
  }
  
  const response: ApiResponse = {
    success: true,
    data: {
      id: connectionInfo.id,
      name: connectionInfo.config.name,
      host: connectionInfo.config.host,
      port: connectionInfo.config.port,
      database: connectionInfo.config.database,
      ssl: connectionInfo.config.ssl,
      status: connectionInfo.status,
      connectedAt: connectionInfo.connectedAt,
      lastActivity: connectionInfo.lastActivity,
      lastError: connectionInfo.lastError,
    },
    message: 'Connection info retrieved successfully'
  };
  
  return res.json(response);
}));

// 删除连接
router.delete('/connections/:id', asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  
  const connectionInfo = connectionManager.getConnectionInfo(id);
  if (!connectionInfo) {
    return res.status(404).json({
      success: false,
      error: 'Connection not found',
    });
  }
  
  try {
    await connectionManager.removeConnection(id);
    
    const response: ApiResponse = {
      success: true,
      message: 'Connection removed successfully'
    };
    
    return res.json(response);
  } catch (error: any) {
    const response: ApiResponse = {
      success: false,
      error: error.message,
      message: 'Failed to remove connection'
    };

    return res.status(500).json(response);
  }
}));

// 获取连接统计信息
router.get('/connections/stats/summary', asyncHandler(async (req: Request, res: Response) => {
  const stats = connectionManager.getConnectionStats();
  
  const response: ApiResponse = {
    success: true,
    data: stats,
    message: 'Connection stats retrieved successfully'
  };
  
  return res.json(response);
}));

export default router;
