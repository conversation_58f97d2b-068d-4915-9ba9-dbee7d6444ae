import React, { useState } from 'react';
import { Modal, Menu, Typography, Space, Divider, Card, Tag, Alert } from 'antd';
import {
  QuestionCircleOutlined,
  DatabaseOutlined,
  CodeOutlined,
  BarChartOutlined,
  ImportOutlined,
  ExportOutlined,
  WifiOutlined,
  SettingOutlined,
} from '@ant-design/icons';

const { Title, Paragraph, Text } = Typography;

interface HelpDocumentationProps {
  visible: boolean;
  onClose: () => void;
}

type HelpSection = 
  | 'overview'
  | 'connections'
  | 'data-browser'
  | 'commands'
  | 'performance'
  | 'import-export'
  | 'realtime'
  | 'settings';

const HelpDocumentation: React.FC<HelpDocumentationProps> = ({ visible, onClose }) => {
  const [selectedSection, setSelectedSection] = useState<HelpSection>('overview');

  const menuItems = [
    {
      key: 'overview',
      icon: <QuestionCircleOutlined />,
      label: '概述',
    },
    {
      key: 'connections',
      icon: <DatabaseOutlined />,
      label: '连接管理',
    },
    {
      key: 'data-browser',
      icon: <DatabaseOutlined />,
      label: '数据浏览',
    },
    {
      key: 'commands',
      icon: <CodeOutlined />,
      label: '命令执行',
    },
    {
      key: 'performance',
      icon: <BarChartOutlined />,
      label: '性能监控',
    },
    {
      key: 'import-export',
      icon: <ImportOutlined />,
      label: '数据导入导出',
    },
    {
      key: 'realtime',
      icon: <WifiOutlined />,
      label: '实时功能',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '设置',
    },
  ];

  const renderContent = () => {
    switch (selectedSection) {
      case 'overview':
        return (
          <div>
            <Title level={3}>Redis Client Solo</Title>
            <Paragraph>
              Redis Client Solo 是一个现代化的 Redis 可视化管理工具，专注于提供优秀的用户体验。
            </Paragraph>
            
            <Title level={4}>主要功能</Title>
            <ul className="space-y-2">
              <li><Text strong>连接管理</Text> - 支持多个 Redis 连接，SSL/TLS 加密连接</li>
              <li><Text strong>数据浏览</Text> - 可视化浏览和编辑 Redis 数据</li>
              <li><Text strong>命令执行</Text> - 内置命令行界面，支持命令历史</li>
              <li><Text strong>性能监控</Text> - 实时监控 Redis 服务器性能</li>
              <li><Text strong>数据导入导出</Text> - 支持 JSON 和 CSV 格式</li>
              <li><Text strong>实时功能</Text> - WebSocket 实时连接状态监控</li>
            </ul>

            <Alert
              message="快速开始"
              description="点击右上角的'连接管理'按钮创建您的第一个 Redis 连接。"
              type="info"
              showIcon
              className="mt-4"
            />
          </div>
        );

      case 'connections':
        return (
          <div>
            <Title level={3}>连接管理</Title>
            
            <Title level={4}>创建连接</Title>
            <Paragraph>
              1. 点击右上角的 <Tag>连接管理</Tag> 按钮<br/>
              2. 点击 <Tag>新建连接</Tag><br/>
              3. 填写连接信息：主机、端口、密码等<br/>
              4. 点击 <Tag>测试连接</Tag> 验证连接<br/>
              5. 点击 <Tag>保存</Tag> 保存连接配置
            </Paragraph>

            <Title level={4}>连接选项</Title>
            <ul>
              <li><Text strong>基本信息</Text>：连接名称、主机地址、端口</li>
              <li><Text strong>认证</Text>：密码、用户名（Redis 6.0+）</li>
              <li><Text strong>SSL/TLS</Text>：启用加密连接</li>
              <li><Text strong>高级选项</Text>：超时设置、重试配置</li>
            </ul>

            <Alert
              message="连接安全"
              description="建议在生产环境中使用 SSL/TLS 加密连接，并设置强密码。"
              type="warning"
              showIcon
            />
          </div>
        );

      case 'data-browser':
        return (
          <div>
            <Title level={3}>数据浏览</Title>
            
            <Title level={4}>浏览数据</Title>
            <Paragraph>
              数据浏览器支持查看和编辑所有 Redis 数据类型：
            </Paragraph>
            <ul>
              <li><Tag color="blue">String</Tag> - 字符串类型</li>
              <li><Tag color="green">Hash</Tag> - 哈希表</li>
              <li><Tag color="orange">List</Tag> - 列表</li>
              <li><Tag color="purple">Set</Tag> - 集合</li>
              <li><Tag color="red">ZSet</Tag> - 有序集合</li>
              <li><Tag color="cyan">Stream</Tag> - 流数据</li>
            </ul>

            <Title level={4}>操作功能</Title>
            <ul>
              <li>搜索和过滤键</li>
              <li>查看键的详细信息和 TTL</li>
              <li>编辑键值</li>
              <li>删除键</li>
              <li>设置过期时间</li>
            </ul>
          </div>
        );

      case 'commands':
        return (
          <div>
            <Title level={3}>命令执行</Title>
            
            <Title level={4}>使用命令行</Title>
            <Paragraph>
              内置的命令行界面支持执行任何 Redis 命令：
            </Paragraph>
            <ul>
              <li>输入 Redis 命令并按 <Tag>Ctrl + Enter</Tag> 执行</li>
              <li>使用 <Tag>↑</Tag> <Tag>↓</Tag> 箭头键浏览命令历史</li>
              <li>支持命令自动补全</li>
              <li>语法高亮显示</li>
            </ul>

            <Title level={4}>常用命令示例</Title>
            <Card size="small" className="bg-gray-50">
              <pre className="text-sm">
{`# 基本操作
SET mykey "Hello World"
GET mykey
DEL mykey

# 列表操作
LPUSH mylist "item1" "item2"
LRANGE mylist 0 -1

# 哈希操作
HSET myhash field1 "value1"
HGETALL myhash`}
              </pre>
            </Card>
          </div>
        );

      case 'performance':
        return (
          <div>
            <Title level={3}>性能监控</Title>
            
            <Title level={4}>监控指标</Title>
            <Paragraph>
              性能监控面板显示以下关键指标：
            </Paragraph>
            <ul>
              <li><Text strong>服务器信息</Text>：版本、运行时间、配置</li>
              <li><Text strong>内存使用</Text>：已用内存、峰值内存、内存碎片率</li>
              <li><Text strong>客户端连接</Text>：连接数、阻塞客户端</li>
              <li><Text strong>操作统计</Text>：命令执行次数、命中率</li>
              <li><Text strong>慢查询日志</Text>：执行时间较长的命令</li>
            </ul>

            <Title level={4}>实时更新</Title>
            <Paragraph>
              监控数据会自动刷新，您也可以手动刷新或调整刷新间隔。
            </Paragraph>
          </div>
        );

      case 'import-export':
        return (
          <div>
            <Title level={3}>数据导入导出</Title>
            
            <Title level={4}>导出数据</Title>
            <ul>
              <li>支持 JSON 和 CSV 格式</li>
              <li>可选择导出特定数据库或键模式</li>
              <li>支持批量导出</li>
            </ul>

            <Title level={4}>导入数据</Title>
            <ul>
              <li>支持从 JSON 和 CSV 文件导入</li>
              <li>批量处理，支持大文件</li>
              <li>导入前预览和验证</li>
              <li>错误处理和报告</li>
            </ul>

            <Alert
              message="注意事项"
              description="导入大量数据时可能会影响 Redis 性能，建议在低峰期进行操作。"
              type="info"
              showIcon
            />
          </div>
        );

      case 'realtime':
        return (
          <div>
            <Title level={3}>实时功能</Title>
            
            <Title level={4}>实时连接监控</Title>
            <Paragraph>
              应用使用 WebSocket 技术提供实时功能：
            </Paragraph>
            <ul>
              <li>实时连接状态监控</li>
              <li>自动重连机制</li>
              <li>连接状态指示器</li>
              <li>实时性能数据更新</li>
            </ul>

            <Title level={4}>状态指示器</Title>
            <Paragraph>
              右上角的状态指示器显示：
            </Paragraph>
            <ul>
              <li><Tag color="green">绿色</Tag> - 连接正常</li>
              <li><Tag color="red">红色</Tag> - 连接错误</li>
              <li><Tag color="default">灰色</Tag> - 未连接</li>
              <li><Tag color="processing">蓝色</Tag> - 连接中</li>
            </ul>
          </div>
        );

      case 'settings':
        return (
          <div>
            <Title level={3}>设置</Title>
            
            <Title level={4}>主题设置</Title>
            <ul>
              <li><Text strong>浅色主题</Text> - 适合白天使用</li>
              <li><Text strong>深色主题</Text> - 适合夜间使用</li>
              <li><Text strong>跟随系统</Text> - 自动跟随系统主题</li>
              <li><Text strong>主题色</Text> - 多种预设颜色可选</li>
            </ul>

            <Title level={4}>键盘快捷键</Title>
            <Paragraph>
              按 <Tag>Ctrl + K</Tag> 查看所有可用的键盘快捷键。
            </Paragraph>

            <Title level={4}>数据持久化</Title>
            <Paragraph>
              应用设置和连接配置会自动保存到本地存储，下次打开时会自动恢复。
            </Paragraph>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Modal
      title={
        <Space>
          <QuestionCircleOutlined />
          <span>帮助文档</span>
        </Space>
      }
      open={visible}
      onCancel={onClose}
      footer={null}
      width={1000}
      className="help-documentation-modal"
    >
      <div className="flex h-96">
        <div className="w-48 border-r border-gray-200 pr-4">
          <Menu
            mode="vertical"
            selectedKeys={[selectedSection]}
            items={menuItems}
            onClick={({ key }) => setSelectedSection(key as HelpSection)}
            className="border-none"
          />
        </div>
        
        <div className="flex-1 pl-6 overflow-y-auto">
          {renderContent()}
        </div>
      </div>
    </Modal>
  );
};

export default HelpDocumentation;
