import React, { useEffect, useState } from 'react';
import {
  Layout,
  Input,
  Select,
  List,
  Card,
  Typography,
  Space,
  Button,
  Tag,
  Spin,
  Empty,
  message,
} from 'antd';
import {
  SearchOutlined,
  ReloadOutlined,
  DatabaseOutlined,
  KeyOutlined,
} from '@ant-design/icons';
import { useRedisStore } from '../stores/redisStore';
import { useConnectionStore } from '../stores/connectionStore';
import KeyValueViewer from './KeyValueViewer';

const { Sider, Content } = Layout;
const { Search } = Input;
const { Option } = Select;
const { Text, Title } = Typography;

const DataBrowser: React.FC = () => {
  const [searchValue, setSearchValue] = useState('*');
  const { activeConnectionId } = useConnectionStore();
  
  const {
    databases,
    currentDatabase,
    keys,
    selectedKey,
    hasMoreKeys,
    loading,
    error,
    fetchDatabases,
    setCurrentDatabase,
    fetchKeys,
    loadMoreKeys,
    setSearchPattern,
    selectKey,
    clearError,
  } = useRedisStore();

  useEffect(() => {
    if (activeConnectionId) {
      fetchDatabases(activeConnectionId);
      fetchKeys(activeConnectionId);
    }
  }, [activeConnectionId, fetchDatabases, fetchKeys]);

  useEffect(() => {
    if (error) {
      message.error(error);
      clearError();
    }
  }, [error, clearError]);

  const handleDatabaseChange = (db: number) => {
    setCurrentDatabase(db);
    if (activeConnectionId) {
      fetchKeys(activeConnectionId);
    }
  };

  const handleSearch = (pattern: string) => {
    setSearchPattern(pattern || '*');
    if (activeConnectionId) {
      fetchKeys(activeConnectionId);
    }
  };

  const handleRefresh = () => {
    if (activeConnectionId) {
      fetchKeys(activeConnectionId);
    }
  };

  const handleLoadMore = () => {
    if (activeConnectionId && hasMoreKeys) {
      loadMoreKeys(activeConnectionId);
    }
  };

  const handleKeySelect = (key: string) => {
    selectKey(key);
  };

  const getTypeColor = (type: string) => {
    const colorMap: Record<string, string> = {
      string: 'blue',
      hash: 'green',
      list: 'orange',
      set: 'purple',
      zset: 'red',
      stream: 'cyan',
    };
    return colorMap[type] || 'default';
  };

  const formatTTL = (ttl: number | null) => {
    if (ttl === null || ttl === -1) return '永不过期';
    if (ttl === 0) return '已过期';
    
    const days = Math.floor(ttl / 86400);
    const hours = Math.floor((ttl % 86400) / 3600);
    const minutes = Math.floor((ttl % 3600) / 60);
    const seconds = ttl % 60;
    
    if (days > 0) return `${days}天${hours}小时`;
    if (hours > 0) return `${hours}小时${minutes}分钟`;
    if (minutes > 0) return `${minutes}分钟${seconds}秒`;
    return `${seconds}秒`;
  };

  if (!activeConnectionId) {
    return (
      <div className="flex items-center justify-center h-full">
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="请先选择一个Redis连接"
        />
      </div>
    );
  }

  return (
    <Layout className="h-full">
      <Sider width={400} className="bg-white border-r">
        <div className="p-4 border-b">
          <Space direction="vertical" className="w-full">
            <div className="flex items-center justify-between">
              <Title level={5} className="mb-0">
                <DatabaseOutlined className="mr-2" />
                数据浏览器
              </Title>
              <Button
                type="text"
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
                loading={loading}
              />
            </div>
            
            <Select
              value={currentDatabase}
              onChange={handleDatabaseChange}
              className="w-full"
              placeholder="选择数据库"
            >
              {databases.map((db) => (
                <Option key={db.db} value={db.db}>
                  DB {db.db} ({db.keys} keys)
                </Option>
              ))}
            </Select>

            <Search
              placeholder="搜索键 (支持通配符 *)"
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              onSearch={handleSearch}
              enterButton={<SearchOutlined />}
            />
          </Space>
        </div>

        <div className="flex-1 overflow-auto">
          <Spin spinning={loading}>
            <List
              dataSource={keys}
              renderItem={(keyInfo) => (
                <List.Item
                  className={`cursor-pointer hover:bg-gray-50 ${
                    selectedKey === keyInfo.key ? 'bg-blue-50 border-l-4 border-blue-500' : ''
                  }`}
                  onClick={() => handleKeySelect(keyInfo.key)}
                >
                  <List.Item.Meta
                    avatar={<KeyOutlined className="text-gray-500" />}
                    title={
                      <div className="flex items-center justify-between">
                        <Text
                          ellipsis={{ tooltip: keyInfo.key }}
                          className="flex-1 mr-2"
                        >
                          {keyInfo.key}
                        </Text>
                        <Tag color={getTypeColor(keyInfo.type)} className="text-xs">
                          {keyInfo.type.toUpperCase()}
                        </Tag>
                      </div>
                    }
                    description={
                      <div className="text-xs text-gray-500">
                        <Space split={<span>|</span>}>
                          <span>大小: {keyInfo.size}</span>
                          <span>TTL: {formatTTL(keyInfo.ttl)}</span>
                        </Space>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
            
            {hasMoreKeys && (
              <div className="p-4 text-center">
                <Button
                  type="link"
                  onClick={handleLoadMore}
                  loading={loading}
                >
                  加载更多
                </Button>
              </div>
            )}
            
            {keys.length === 0 && !loading && (
              <Empty
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description="没有找到匹配的键"
                className="mt-8"
              />
            )}
          </Spin>
        </div>
      </Sider>

      <Content className="bg-white">
        {selectedKey ? (
          <KeyValueViewer
            connectionId={activeConnectionId}
            keyName={selectedKey}
            database={currentDatabase}
          />
        ) : (
          <div className="flex items-center justify-center h-full">
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description="请选择一个键来查看其值"
            />
          </div>
        )}
      </Content>
    </Layout>
  );
};

export default DataBrowser;
