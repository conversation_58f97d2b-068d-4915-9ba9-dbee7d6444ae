"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const RedisConnectionManager_1 = require("../services/RedisConnectionManager");
const asyncHandler_1 = require("../utils/asyncHandler");
const router = (0, express_1.Router)();
router.get('/connections', (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    const connections = RedisConnectionManager_1.connectionManager.getAllConnections();
    const response = {
        success: true,
        data: connections.map(conn => ({
            id: conn.id,
            name: conn.config.name,
            host: conn.config.host,
            port: conn.config.port,
            database: conn.config.database,
            ssl: conn.config.ssl,
            status: conn.status,
            connectedAt: conn.connectedAt,
            lastActivity: conn.lastActivity,
            lastError: conn.lastError,
        })),
        message: 'Connections retrieved successfully'
    };
    return res.json(response);
}));
router.post('/connections', (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    const config = req.body;
    if (!config.name || !config.host || !config.port) {
        return res.status(400).json({
            success: false,
            error: 'Missing required fields: name, host, port',
        });
    }
    if (!config.id) {
        config.id = `conn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    config.database = config.database || 0;
    config.ssl = config.ssl || false;
    config.timeout = config.timeout || 5000;
    config.retryDelayOnFailover = config.retryDelayOnFailover || 100;
    config.maxRetriesPerRequest = config.maxRetriesPerRequest || 3;
    config.lazyConnect = config.lazyConnect !== false;
    try {
        const connectionId = await RedisConnectionManager_1.connectionManager.createConnection(config);
        const response = {
            success: true,
            data: { connectionId },
            message: 'Connection created successfully'
        };
        return res.status(201).json(response);
    }
    catch (error) {
        const response = {
            success: false,
            error: error.message,
            message: 'Failed to create connection'
        };
        return res.status(500).json(response);
    }
}));
router.post('/connections/test', (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    const config = req.body;
    if (!config.host || !config.port) {
        return res.status(400).json({
            success: false,
            error: 'Missing required fields: host, port',
        });
    }
    try {
        const isConnectable = await RedisConnectionManager_1.connectionManager.testConnection(config);
        const response = {
            success: true,
            data: { connectable: isConnectable },
            message: isConnectable ? 'Connection test successful' : 'Connection test failed'
        };
        return res.json(response);
    }
    catch (error) {
        const response = {
            success: false,
            error: error.message,
            message: 'Connection test failed'
        };
        return res.status(500).json(response);
    }
}));
router.get('/connections/:id', (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const connectionInfo = RedisConnectionManager_1.connectionManager.getConnectionInfo(id);
    if (!connectionInfo) {
        return res.status(404).json({
            success: false,
            error: 'Connection not found',
        });
    }
    const response = {
        success: true,
        data: {
            id: connectionInfo.id,
            name: connectionInfo.config.name,
            host: connectionInfo.config.host,
            port: connectionInfo.config.port,
            database: connectionInfo.config.database,
            ssl: connectionInfo.config.ssl,
            status: connectionInfo.status,
            connectedAt: connectionInfo.connectedAt,
            lastActivity: connectionInfo.lastActivity,
            lastError: connectionInfo.lastError,
        },
        message: 'Connection info retrieved successfully'
    };
    return res.json(response);
}));
router.delete('/connections/:id', (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    const { id } = req.params;
    const connectionInfo = RedisConnectionManager_1.connectionManager.getConnectionInfo(id);
    if (!connectionInfo) {
        return res.status(404).json({
            success: false,
            error: 'Connection not found',
        });
    }
    try {
        await RedisConnectionManager_1.connectionManager.removeConnection(id);
        const response = {
            success: true,
            message: 'Connection removed successfully'
        };
        return res.json(response);
    }
    catch (error) {
        const response = {
            success: false,
            error: error.message,
            message: 'Failed to remove connection'
        };
        return res.status(500).json(response);
    }
}));
router.get('/connections/stats/summary', (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    const stats = RedisConnectionManager_1.connectionManager.getConnectionStats();
    const response = {
        success: true,
        data: stats,
        message: 'Connection stats retrieved successfully'
    };
    return res.json(response);
}));
exports.default = router;
//# sourceMappingURL=connections.js.map