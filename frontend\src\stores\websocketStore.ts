import { create } from 'zustand';
import websocketService, { ConnectionStatus, RealtimeStats, ConnectionInfo } from '../services/websocket';

interface WebSocketState {
  // 连接状态
  isConnected: boolean;
  clientId: string | null;
  connectionError: string | null;
  
  // 订阅的连接
  subscribedConnectionId: string | null;
  
  // 连接状态数据
  connectionStatuses: Map<string, ConnectionStatus>;
  
  // 实时统计数据
  realtimeStats: Map<string, RealtimeStats>;
  
  // 连接信息
  connectionInfos: Map<string, ConnectionInfo>;
  
  // 操作方法
  subscribeToConnection: (connectionId: string) => void;
  unsubscribeFromConnection: () => void;
  requestRealtimeData: (connectionId: string, type: 'stats' | 'info') => void;
  reconnect: () => void;
  
  // 内部方法
  setConnected: (connected: boolean, clientId?: string) => void;
  setConnectionError: (error: string | null) => void;
  updateConnectionStatus: (status: ConnectionStatus) => void;
  updateRealtimeStats: (stats: RealtimeStats) => void;
  updateConnectionInfo: (info: ConnectionInfo) => void;
}

export const useWebSocketStore = create<WebSocketState>((set, get) => {
  // 设置WebSocket事件监听器
  const setupEventListeners = () => {
    websocketService.on('connected', () => {
      set({ isConnected: true, connectionError: null });
    });

    websocketService.on('disconnected', () => {
      set({ isConnected: false });
    });

    websocketService.on('error', (data: { error: string }) => {
      set({ connectionError: data.error });
    });

    websocketService.on('server-connected', (data: { clientId: string }) => {
      set({ clientId: data.clientId, isConnected: true, connectionError: null });
    });

    websocketService.on('connection-status', (status: ConnectionStatus) => {
      get().updateConnectionStatus(status);
    });

    websocketService.on('realtime-stats', (stats: RealtimeStats) => {
      get().updateRealtimeStats(stats);
    });

    websocketService.on('connection-info', (info: ConnectionInfo) => {
      get().updateConnectionInfo(info);
    });

    websocketService.on('server-error', (data: { message: string }) => {
      console.error('WebSocket server error:', data.message);
    });

    websocketService.on('max-reconnect-attempts', () => {
      set({ connectionError: 'Failed to reconnect to server after multiple attempts' });
    });
  };

  // 初始化事件监听器
  setupEventListeners();

  return {
    // 初始状态
    isConnected: websocketService.isConnected(),
    clientId: websocketService.getClientId() || null,
    connectionError: null,
    subscribedConnectionId: null,
    connectionStatuses: new Map(),
    realtimeStats: new Map(),
    connectionInfos: new Map(),

    // 操作方法
    subscribeToConnection: (connectionId: string) => {
      const currentSubscription = get().subscribedConnectionId;
      
      // 如果已经订阅了相同的连接，不需要重复订阅
      if (currentSubscription === connectionId) {
        return;
      }
      
      // 如果订阅了其他连接，先取消订阅
      if (currentSubscription) {
        websocketService.unsubscribeFromConnection();
      }
      
      // 订阅新连接
      websocketService.subscribeToConnection(connectionId);
      set({ subscribedConnectionId: connectionId });
      
      // 请求初始数据
      websocketService.requestRealtimeData(connectionId, 'stats');
      websocketService.requestRealtimeData(connectionId, 'info');
    },

    unsubscribeFromConnection: () => {
      websocketService.unsubscribeFromConnection();
      set({ subscribedConnectionId: null });
    },

    requestRealtimeData: (connectionId: string, type: 'stats' | 'info') => {
      websocketService.requestRealtimeData(connectionId, type);
    },

    reconnect: () => {
      websocketService.reconnect();
    },

    // 内部状态更新方法
    setConnected: (connected: boolean, clientId?: string) => {
      set({ 
        isConnected: connected, 
        clientId: clientId || null,
        connectionError: connected ? null : get().connectionError
      });
    },

    setConnectionError: (error: string | null) => {
      set({ connectionError: error });
    },

    updateConnectionStatus: (status: ConnectionStatus) => {
      set((state) => {
        const newStatuses = new Map(state.connectionStatuses);
        newStatuses.set(status.connectionId, status);
        return { connectionStatuses: newStatuses };
      });
    },

    updateRealtimeStats: (stats: RealtimeStats) => {
      set((state) => {
        const newStats = new Map(state.realtimeStats);
        newStats.set(stats.connectionId, stats);
        return { realtimeStats: newStats };
      });
    },

    updateConnectionInfo: (info: ConnectionInfo) => {
      set((state) => {
        const newInfos = new Map(state.connectionInfos);
        newInfos.set(info.connectionId, info);
        return { connectionInfos: newInfos };
      });
    },
  };
});

// 便捷的hooks
export const useWebSocketConnection = () => {
  const { isConnected, connectionError, reconnect } = useWebSocketStore();
  return { isConnected, connectionError, reconnect };
};

export const useConnectionStatus = (connectionId: string | null) => {
  const { connectionStatuses, subscribeToConnection, unsubscribeFromConnection } = useWebSocketStore();
  
  const status = connectionId ? connectionStatuses.get(connectionId) : null;
  
  return {
    status,
    subscribe: subscribeToConnection,
    unsubscribe: unsubscribeFromConnection,
  };
};

export const useRealtimeStats = (connectionId: string | null) => {
  const { realtimeStats, requestRealtimeData } = useWebSocketStore();
  
  const stats = connectionId ? realtimeStats.get(connectionId) : null;
  
  return {
    stats,
    requestStats: (id: string) => requestRealtimeData(id, 'stats'),
  };
};

export const useConnectionInfo = (connectionId: string | null) => {
  const { connectionInfos, requestRealtimeData } = useWebSocketStore();
  
  const info = connectionId ? connectionInfos.get(connectionId) : null;
  
  return {
    info,
    requestInfo: (id: string) => requestRealtimeData(id, 'info'),
  };
};
