import axios, { AxiosResponse } from 'axios';

// API基础配置
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api';

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    console.log(`API Response: ${response.status} ${response.config.url}`);
    return response;
  },
  (error) => {
    console.error('API Response Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Redis连接配置类型
export interface RedisConnectionConfig {
  id?: string;
  name: string;
  host: string;
  port: number;
  password?: string;
  database?: number;
  ssl?: boolean;
  timeout?: number;
  lazyConnect?: boolean;
  maxRetriesPerRequest?: number;
}

// 连接信息类型
export interface ConnectionInfo {
  id: string;
  name: string;
  host: string;
  port: number;
  database: number;
  ssl: boolean;
  status: 'connected' | 'disconnected' | 'connecting' | 'error';
  connectedAt?: string;
  lastActivity?: string;
  lastError?: string;
}

// Redis键信息类型
export interface RedisKeyInfo {
  key: string;
  type: string;
  ttl: number | null;
  size: number;
  error?: string;
}

// Redis键值类型
export interface RedisKeyValue {
  key: string;
  type: string;
  value: any;
  ttl: number | null;
}

// 数据库信息类型
export interface DatabaseInfo {
  db: number;
  keys: number;
  expires: number;
}

// API服务类
export class ApiService {
  // 健康检查
  static async healthCheck(): Promise<ApiResponse> {
    const response = await apiClient.get('/health');
    return response.data;
  }

  // 连接管理API
  static async getConnections(): Promise<ApiResponse<ConnectionInfo[]>> {
    const response = await apiClient.get('/connections');
    return response.data;
  }

  static async createConnection(config: RedisConnectionConfig): Promise<ApiResponse<{ connectionId: string }>> {
    const response = await apiClient.post('/connections', config);
    return response.data;
  }

  static async testConnection(config: RedisConnectionConfig): Promise<ApiResponse<{ connectable: boolean }>> {
    const response = await apiClient.post('/connections/test', config);
    return response.data;
  }

  static async getConnection(id: string): Promise<ApiResponse<ConnectionInfo>> {
    const response = await apiClient.get(`/connections/${id}`);
    return response.data;
  }

  static async deleteConnection(id: string): Promise<ApiResponse> {
    const response = await apiClient.delete(`/connections/${id}`);
    return response.data;
  }

  static async getConnectionStats(): Promise<ApiResponse> {
    const response = await apiClient.get('/connections/stats');
    return response.data;
  }

  // Redis数据操作API
  static async getRedisInfo(connectionId: string): Promise<ApiResponse> {
    const response = await apiClient.get(`/redis/${connectionId}/info`);
    return response.data;
  }

  static async getDatabases(connectionId: string): Promise<ApiResponse<DatabaseInfo[]>> {
    const response = await apiClient.get(`/redis/${connectionId}/databases`);
    return response.data;
  }

  static async getKeys(
    connectionId: string,
    options: {
      pattern?: string;
      cursor?: string;
      count?: number;
      db?: number;
    } = {}
  ): Promise<ApiResponse<{ cursor: string; keys: RedisKeyInfo[]; hasMore: boolean }>> {
    const params = new URLSearchParams();
    if (options.pattern) params.append('pattern', options.pattern);
    if (options.cursor) params.append('cursor', options.cursor);
    if (options.count) params.append('count', options.count.toString());
    if (options.db !== undefined) params.append('db', options.db.toString());

    const response = await apiClient.get(`/redis/${connectionId}/keys?${params.toString()}`);
    return response.data;
  }

  static async getKeyValue(
    connectionId: string,
    key: string,
    db: number = 0
  ): Promise<ApiResponse<RedisKeyValue>> {
    const response = await apiClient.get(`/redis/${connectionId}/key/${encodeURIComponent(key)}?db=${db}`);
    return response.data;
  }

  static async executeCommand(
    connectionId: string,
    command: string,
    args: string[] = [],
    db: number = 0
  ): Promise<ApiResponse<{ result: any }>> {
    const response = await apiClient.post(`/redis/${connectionId}/command`, {
      command,
      args,
      db,
    });
    return response.data;
  }

  // 获取Redis性能统计
  static async getRedisStats(connectionId: string): Promise<ApiResponse<RedisStats>> {
    const response = await apiClient.get(`/redis/${connectionId}/stats`);
    return response.data;
  }

  // 导出Redis数据
  static async exportRedisData(
    connectionId: string,
    options: {
      format?: 'json' | 'csv';
      pattern?: string;
      db?: number;
    } = {}
  ): Promise<Blob> {
    const params = new URLSearchParams();
    if (options.format) params.append('format', options.format);
    if (options.pattern) params.append('pattern', options.pattern);
    if (options.db !== undefined) params.append('db', options.db.toString());

    const response = await apiClient.get(`/redis/${connectionId}/export?${params.toString()}`, {
      responseType: 'blob',
    });
    return response.data;
  }

  // 导入Redis数据
  static async importRedisData(
    connectionId: string,
    data: any,
    options: {
      db?: number;
      overwrite?: boolean;
    } = {}
  ): Promise<ApiResponse<ImportResult>> {
    const response = await apiClient.post(`/redis/${connectionId}/import`, {
      data,
      db: options.db || 0,
      overwrite: options.overwrite || false,
    });
    return response.data;
  }
}

// Redis性能统计类型
export interface RedisStats {
  server: {
    version: string;
    uptime: number;
    mode: string;
  };
  memory: {
    used: number;
    used_human: string;
    peak: number;
    peak_human: string;
    rss: number;
    rss_human: string;
    fragmentation_ratio: number;
  };
  clients: {
    connected: number;
    blocked: number;
    total: number;
  };
  stats: {
    total_connections_received: number;
    total_commands_processed: number;
    instantaneous_ops_per_sec: number;
    keyspace_hits: number;
    keyspace_misses: number;
    hit_rate: string;
  };
  cpu: {
    used_cpu_sys: number;
    used_cpu_user: number;
    used_cpu_sys_children: number;
    used_cpu_user_children: number;
  };
  keyspace: Record<string, any>;
  slowlog: Array<{
    id: number;
    timestamp: number;
    duration: number;
    command: string[];
  }>;
}

// 导入结果类型
export interface ImportResult {
  total: number;
  success: number;
  failed: number;
  skipped: number;
  errors: string[];
}

export default ApiService;
