{"name": "@redis-client-solo/frontend", "private": true, "version": "1.0.0", "type": "module", "description": "Redis可视化工具前端应用 - 用户体验优先设计", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui"}, "dependencies": {"@ant-design/icons": "^5.2.6", "@redis-client-solo/shared": "file:../shared", "antd": "^5.12.0", "axios": "^1.6.0", "classnames": "^2.3.2", "dayjs": "^1.11.10", "echarts": "^5.4.3", "echarts-for-react": "^3.0.2", "lodash-es": "^4.17.21", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hotkeys-hook": "^4.4.1", "react-router-dom": "^6.20.0", "react-virtualized-auto-sizer": "^1.0.20", "react-window": "^1.8.8", "socket.io-client": "^4.8.1", "zustand": "^4.5.7"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/lodash-es": "^4.17.12", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/react-window": "^1.8.8", "@vitejs/plugin-react": "^4.6.0", "@vitest/ui": "^1.0.0", "autoprefixer": "^10.4.16", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^5.4.0", "vitest": "^1.0.0"}}