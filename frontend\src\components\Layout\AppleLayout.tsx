import React, { useState } from 'react';
import { Layout, Button, Typo<PERSON>, Badge, Tooltip, Dropdown } from 'antd';
import type { MenuProps } from 'antd';
import {
  DatabaseOutlined,
  TableOutlined,
  CodeOutlined,
  ThunderboltOutlined,
  ImportOutlined,
  SettingOutlined,
  DisconnectOutlined,
  MenuOutlined,
  CloseOutlined,
} from '@ant-design/icons';
import { useConnectionStore } from '../../stores/connectionStore';
import { useTheme } from '../../stores/themeStore';
import ThemeToggle from '../ThemeToggle';
import RealtimeStatusIndicator from '../RealtimeStatusIndicator';

const { Header, Content, Sider } = Layout;
const { Text } = Typography;

interface AppleLayoutProps {
  children: React.ReactNode;
  activeTab: string;
  onTabChange: (tab: string) => void;
  onShowConnectionManager: () => void;
  onDisconnect: () => void;
}

const AppleLayout: React.FC<AppleLayoutProps> = ({
  children,
  activeTab,
  onTabChange,
  onShowConnectionManager,
  onDisconnect,
}) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const { connections } = useConnectionStore();
  const activeConnection = connections.find(conn => conn.id === 'active'); // 临时修复

  // 导航菜单项
  const navigationItems = [
    {
      key: 'browser',
      icon: <TableOutlined />,
      label: '数据浏览器',
      description: '浏览和管理Redis数据',
    },
    {
      key: 'command',
      icon: <CodeOutlined />,
      label: '命令执行器',
      description: '执行Redis命令',
    },
    {
      key: 'monitor',
      icon: <ThunderboltOutlined />,
      label: '性能监控',
      description: '监控Redis性能指标',
    },
    {
      key: 'import-export',
      icon: <ImportOutlined />,
      label: '导入导出',
      description: '数据导入导出工具',
    },
  ];

  // 连接操作菜单
  const connectionMenuItems: MenuProps['items'] = [
    {
      key: 'manage',
      icon: <DatabaseOutlined />,
      label: '管理连接',
      onClick: onShowConnectionManager,
    },
    {
      key: 'disconnect',
      icon: <DisconnectOutlined />,
      label: '断开连接',
      onClick: onDisconnect,
      danger: true,
    },
  ];

  return (
    <Layout className="h-screen bg-[var(--bg-primary)]">
      {/* 顶部导航栏 */}
      <Header className="apple-glass border-b border-[var(--border-primary)] px-6 h-16 flex items-center justify-between">
        <div className="flex items-center space-x-4">
          {/* Logo和标题 */}
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-br from-[var(--apple-blue-500)] to-[var(--apple-blue-600)] rounded-lg flex items-center justify-center">
              <DatabaseOutlined className="text-white text-lg" />
            </div>
            <div>
              <Text className="text-[var(--text-primary)] font-semibold text-lg">
                Redis Client Solo
              </Text>
              {activeConnection && (
                <div className="text-xs text-[var(--text-secondary)]">
                  {activeConnection.name}
                </div>
              )}
            </div>
          </div>

          {/* 连接状态指示器 */}
          {activeConnection && (
            <div className="flex items-center space-x-2">
              <RealtimeStatusIndicator />
              <Badge
                status="success"
                text={
                  <Text className="text-[var(--text-secondary)] text-sm">
                    {activeConnection.host}:{activeConnection.port}
                  </Text>
                }
              />
            </div>
          )}
        </div>

        {/* 右侧操作区 */}
        <div className="flex items-center space-x-2">
          <ThemeToggle />
          
          {activeConnection && (
            <Dropdown
              menu={{ items: connectionMenuItems }}
              trigger={['click']}
              placement="bottomRight"
            >
              <Button
                type="text"
                icon={<SettingOutlined />}
                className="text-[var(--text-secondary)] hover:text-[var(--text-primary)]"
              />
            </Dropdown>
          )}

          {/* 移动端菜单按钮 */}
          <Button
            type="text"
            icon={sidebarCollapsed ? <MenuOutlined /> : <CloseOutlined />}
            onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
            className="md:hidden text-[var(--text-secondary)] hover:text-[var(--text-primary)]"
          />
        </div>
      </Header>

      <Layout>
        {/* 侧边导航栏 */}
        {activeConnection && (
          <Sider
            width={280}
            collapsed={sidebarCollapsed}
            collapsedWidth={0}
            className="bg-[var(--bg-secondary)] border-r border-[var(--border-primary)] transition-all duration-300"
            breakpoint="md"
            onBreakpoint={(broken) => setSidebarCollapsed(broken)}
          >
            <div className="p-4">
              <Text className="text-[var(--text-secondary)] text-xs font-medium uppercase tracking-wider">
                导航
              </Text>
            </div>
            
            <nav className="px-2">
              {navigationItems.map((item) => (
                <Tooltip
                  key={item.key}
                  title={sidebarCollapsed ? item.label : ''}
                  placement="right"
                >
                  <button
                    onClick={() => onTabChange(item.key)}
                    className={`
                      w-full flex items-center space-x-3 px-4 py-3 mb-1 rounded-xl
                      transition-all duration-200 text-left
                      ${activeTab === item.key
                        ? 'bg-[var(--apple-blue-500)] text-white shadow-lg'
                        : 'text-[var(--text-secondary)] hover:bg-[var(--bg-tertiary)] hover:text-[var(--text-primary)]'
                      }
                    `}
                  >
                    <span className="text-lg">{item.icon}</span>
                    {!sidebarCollapsed && (
                      <div className="flex-1">
                        <div className="font-medium">{item.label}</div>
                        <div className="text-xs opacity-75 mt-0.5">
                          {item.description}
                        </div>
                      </div>
                    )}
                  </button>
                </Tooltip>
              ))}
            </nav>

            {/* 侧边栏底部信息 */}
            {!sidebarCollapsed && (
              <div className="absolute bottom-4 left-4 right-4">
                <div className="apple-card p-3">
                  <div className="flex items-center space-x-2 mb-2">
                    <div className="w-2 h-2 bg-[var(--apple-success)] rounded-full"></div>
                    <Text className="text-[var(--text-secondary)] text-xs">
                      连接正常
                    </Text>
                  </div>
                  <Text className="text-[var(--text-tertiary)] text-xs">
                    DB {activeConnection.database} • {activeConnection.host}
                  </Text>
                </div>
              </div>
            )}
          </Sider>
        )}

        {/* 主内容区 */}
        <Content className="bg-[var(--bg-primary)] overflow-hidden">
          {activeConnection ? (
            <div className="h-full p-6">
              <div className="h-full apple-card overflow-hidden">
                {children}
              </div>
            </div>
          ) : (
            // 欢迎页面
            <div className="h-full flex items-center justify-center">
              <div className="text-center max-w-md">
                <div className="w-24 h-24 bg-gradient-to-br from-[var(--apple-blue-500)] to-[var(--apple-blue-600)] rounded-3xl flex items-center justify-center mx-auto mb-8">
                  <DatabaseOutlined className="text-white text-4xl" />
                </div>
                
                <h1 className="text-3xl font-bold text-[var(--text-primary)] mb-4">
                  欢迎使用 Redis Client Solo
                </h1>
                
                <p className="text-[var(--text-secondary)] text-lg mb-8 leading-relaxed">
                  现代化的Redis可视化管理工具<br />
                  让Redis管理变得简单高效
                </p>
                
                <Button
                  type="primary"
                  size="large"
                  icon={<DatabaseOutlined />}
                  onClick={onShowConnectionManager}
                  className="apple-button apple-button-primary h-12 px-8 text-base font-medium"
                >
                  开始连接Redis服务器
                </Button>
                
                <div className="mt-8 flex justify-center space-x-6 text-sm text-[var(--text-tertiary)]">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-[var(--apple-success)] rounded-full"></div>
                    <span>安全连接</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-[var(--apple-blue-500)] rounded-full"></div>
                    <span>实时监控</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-[var(--apple-warning)] rounded-full"></div>
                    <span>智能分析</span>
                  </div>
                </div>
              </div>
            </div>
          )}
        </Content>
      </Layout>
    </Layout>
  );
};

export default AppleLayout;
