import React, { useState, useRef, useEffect } from 'react';
import {
  Card,
  Input,
  Button,
  Typography,
  Space,
  List,
  Tag,
  message,
  Divider,
} from 'antd';
import {
  PlayCircleOutlined,
  ClearOutlined,
  CopyOutlined,
  HistoryOutlined,
} from '@ant-design/icons';
import { useRedisStore } from '../stores/redisStore';
import { useConnectionStore } from '../stores/connectionStore';

const { TextArea } = Input;
const { Text, Title } = Typography;

interface CommandHistory {
  id: string;
  command: string;
  args: string[];
  result: any;
  timestamp: Date;
  success: boolean;
  error?: string;
}

const CommandExecutor: React.FC = () => {
  const [command, setCommand] = useState('');
  const [history, setHistory] = useState<CommandHistory[]>([]);
  const [executing, setExecuting] = useState(false);
  const inputRef = useRef<any>(null);
  
  const { activeConnectionId } = useConnectionStore();
  const { executeCommand, currentDatabase } = useRedisStore();

  useEffect(() => {
    // 从localStorage加载历史记录
    const savedHistory = localStorage.getItem('redis-command-history');
    if (savedHistory) {
      try {
        const parsed = JSON.parse(savedHistory);
        setHistory(parsed.map((item: any) => ({
          ...item,
          timestamp: new Date(item.timestamp),
        })));
      } catch (error) {
        console.error('Failed to load command history:', error);
      }
    }
  }, []);

  const saveHistory = (newHistory: CommandHistory[]) => {
    setHistory(newHistory);
    localStorage.setItem('redis-command-history', JSON.stringify(newHistory));
  };

  const parseCommand = (input: string): { command: string; args: string[] } => {
    const parts = input.trim().split(/\s+/);
    const command = parts[0] || '';
    const args = parts.slice(1);
    return { command, args };
  };

  const handleExecute = async () => {
    if (!command.trim() || !activeConnectionId) {
      return;
    }

    const { command: cmd, args } = parseCommand(command);
    setExecuting(true);

    try {
      const result = await executeCommand(activeConnectionId, cmd, args);
      
      const historyItem: CommandHistory = {
        id: Date.now().toString(),
        command: cmd,
        args,
        result,
        timestamp: new Date(),
        success: true,
      };

      const newHistory = [historyItem, ...history.slice(0, 99)]; // 保留最近100条
      saveHistory(newHistory);
      
      message.success('命令执行成功');
      setCommand('');
    } catch (error: any) {
      const historyItem: CommandHistory = {
        id: Date.now().toString(),
        command: cmd,
        args,
        result: null,
        timestamp: new Date(),
        success: false,
        error: error.message,
      };

      const newHistory = [historyItem, ...history.slice(0, 99)];
      saveHistory(newHistory);
      
      message.error(`命令执行失败: ${error.message}`);
    } finally {
      setExecuting(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault();
      handleExecute();
    }
  };

  const handleClearHistory = () => {
    setHistory([]);
    localStorage.removeItem('redis-command-history');
    message.success('历史记录已清空');
  };

  const handleCopyCommand = (cmd: string, args: string[]) => {
    const fullCommand = [cmd, ...args].join(' ');
    navigator.clipboard.writeText(fullCommand).then(() => {
      message.success('命令已复制到剪贴板');
    });
  };

  const handleCopyResult = (result: any) => {
    const text = typeof result === 'string' ? result : JSON.stringify(result, null, 2);
    navigator.clipboard.writeText(text).then(() => {
      message.success('结果已复制到剪贴板');
    });
  };

  const handleUseCommand = (cmd: string, args: string[]) => {
    const fullCommand = [cmd, ...args].join(' ');
    setCommand(fullCommand);
    inputRef.current?.focus();
  };

  const formatResult = (result: any) => {
    if (result === null || result === undefined) {
      return '(nil)';
    }
    
    if (typeof result === 'string') {
      return result;
    }
    
    if (Array.isArray(result)) {
      return result.map((item, index) => `${index + 1}) ${item}`).join('\n');
    }
    
    if (typeof result === 'object') {
      return JSON.stringify(result, null, 2);
    }
    
    return String(result);
  };

  const formatTimestamp = (timestamp: Date) => {
    return timestamp.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  };

  if (!activeConnectionId) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <Title level={4} type="secondary">
            请先选择一个Redis连接
          </Title>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      <Card className="mb-4">
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <Title level={5} className="mb-0">
              Redis命令执行器
            </Title>
            <Space>
              <Text type="secondary">数据库: {currentDatabase}</Text>
              <Tag color="blue">Ctrl+Enter 执行</Tag>
            </Space>
          </div>
        </div>

        <div className="mb-4">
          <TextArea
            ref={inputRef}
            value={command}
            onChange={(e) => setCommand(e.target.value)}
            onKeyDown={handleKeyPress}
            placeholder="输入Redis命令，例如: GET mykey"
            rows={3}
            className="font-mono"
          />
        </div>

        <div className="flex justify-between">
          <Space>
            <Button
              type="primary"
              icon={<PlayCircleOutlined />}
              onClick={handleExecute}
              loading={executing}
              disabled={!command.trim()}
            >
              执行命令
            </Button>
            <Button
              icon={<ClearOutlined />}
              onClick={() => setCommand('')}
              disabled={!command.trim()}
            >
              清空
            </Button>
          </Space>
          
          <Button
            icon={<ClearOutlined />}
            onClick={handleClearHistory}
            disabled={history.length === 0}
          >
            清空历史
          </Button>
        </div>
      </Card>

      <Card
        title={
          <Space>
            <HistoryOutlined />
            <span>执行历史</span>
            <Tag>{history.length}</Tag>
          </Space>
        }
        className="flex-1 overflow-hidden"
        bodyStyle={{ height: '100%', overflow: 'auto', padding: 0 }}
      >
        <List
          dataSource={history}
          renderItem={(item) => (
            <List.Item className="px-4">
              <div className="w-full">
                <div className="flex items-center justify-between mb-2">
                  <Space>
                    <Tag color={item.success ? 'green' : 'red'}>
                      {item.success ? '成功' : '失败'}
                    </Tag>
                    <Text type="secondary" className="text-xs">
                      {formatTimestamp(item.timestamp)}
                    </Text>
                  </Space>
                  
                  <Space>
                    <Button
                      type="text"
                      size="small"
                      icon={<CopyOutlined />}
                      onClick={() => handleCopyCommand(item.command, item.args)}
                    />
                    <Button
                      type="text"
                      size="small"
                      onClick={() => handleUseCommand(item.command, item.args)}
                    >
                      使用
                    </Button>
                  </Space>
                </div>
                
                <div className="mb-2">
                  <Text strong className="font-mono">
                    {item.command} {item.args.join(' ')}
                  </Text>
                </div>
                
                {item.success ? (
                  <div className="bg-gray-50 p-2 rounded border-l-4 border-green-500">
                    <div className="flex items-start justify-between">
                      <pre className="font-mono text-sm whitespace-pre-wrap flex-1 mr-2">
                        {formatResult(item.result)}
                      </pre>
                      <Button
                        type="text"
                        size="small"
                        icon={<CopyOutlined />}
                        onClick={() => handleCopyResult(item.result)}
                      />
                    </div>
                  </div>
                ) : (
                  <div className="bg-red-50 p-2 rounded border-l-4 border-red-500">
                    <Text type="danger" className="font-mono text-sm">
                      {item.error}
                    </Text>
                  </div>
                )}
              </div>
            </List.Item>
          )}
        />
        
        {history.length === 0 && (
          <div className="flex items-center justify-center h-32">
            <Text type="secondary">暂无执行历史</Text>
          </div>
        )}
      </Card>
    </div>
  );
};

export default CommandExecutor;
