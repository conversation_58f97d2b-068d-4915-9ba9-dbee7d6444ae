/* Apple-Inspired Design System for Redis Client */
@import './apple-animations.css';

/* ===== 色彩系统 ===== */
:root {
  /* 主色调 - 基于苹果的蓝色系统 */
  --apple-blue-50: #f0f9ff;
  --apple-blue-100: #e0f2fe;
  --apple-blue-200: #bae6fd;
  --apple-blue-300: #7dd3fc;
  --apple-blue-400: #38bdf8;
  --apple-blue-500: #0ea5e9;
  --apple-blue-600: #0284c7;
  --apple-blue-700: #0369a1;
  --apple-blue-800: #075985;
  --apple-blue-900: #0c4a6e;

  /* 中性色 - 苹果风格的灰色系统 */
  --apple-gray-50: #fafafa;
  --apple-gray-100: #f5f5f5;
  --apple-gray-200: #e5e5e5;
  --apple-gray-300: #d4d4d4;
  --apple-gray-400: #a3a3a3;
  --apple-gray-500: #737373;
  --apple-gray-600: #525252;
  --apple-gray-700: #404040;
  --apple-gray-800: #262626;
  --apple-gray-900: #171717;

  /* 语义色彩 */
  --apple-success: #34d399;
  --apple-warning: #fbbf24;
  --apple-error: #f87171;
  --apple-info: var(--apple-blue-500);

  /* 浅色主题 */
  --bg-primary: #ffffff;
  --bg-secondary: var(--apple-gray-50);
  --bg-tertiary: var(--apple-gray-100);
  --bg-elevated: #ffffff;
  --bg-overlay: rgba(255, 255, 255, 0.8);
  
  --text-primary: var(--apple-gray-900);
  --text-secondary: var(--apple-gray-600);
  --text-tertiary: var(--apple-gray-400);
  --text-inverse: #ffffff;
  
  --border-primary: var(--apple-gray-200);
  --border-secondary: var(--apple-gray-100);
  --border-focus: var(--apple-blue-500);
  
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* 深色主题 */
:root.dark {
  --bg-primary: #000000;
  --bg-secondary: #1c1c1e;
  --bg-tertiary: #2c2c2e;
  --bg-elevated: #1c1c1e;
  --bg-overlay: rgba(28, 28, 30, 0.8);
  
  --text-primary: #ffffff;
  --text-secondary: var(--apple-gray-300);
  --text-tertiary: var(--apple-gray-500);
  --text-inverse: var(--apple-gray-900);
  
  --border-primary: var(--apple-gray-700);
  --border-secondary: var(--apple-gray-800);
  --border-focus: var(--apple-blue-400);
  
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.3);
}

/* ===== 字体系统 ===== */
:root {
  /* 苹果风格字体栈 */
  --font-system: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  --font-mono: 'SF Mono', 'Monaco', 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  
  /* 字体大小 - 基于苹果的类型比例 */
  --text-xs: 0.75rem;    /* 12px */
  --text-sm: 0.875rem;   /* 14px */
  --text-base: 1rem;     /* 16px */
  --text-lg: 1.125rem;   /* 18px */
  --text-xl: 1.25rem;    /* 20px */
  --text-2xl: 1.5rem;    /* 24px */
  --text-3xl: 1.875rem;  /* 30px */
  --text-4xl: 2.25rem;   /* 36px */
  --text-5xl: 3rem;      /* 48px */
  
  /* 行高 */
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  
  /* 字重 */
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
}

/* ===== 间距系统 ===== */
:root {
  /* 基于8px网格的间距系统 */
  --space-0: 0;
  --space-1: 0.25rem;  /* 4px */
  --space-2: 0.5rem;   /* 8px */
  --space-3: 0.75rem;  /* 12px */
  --space-4: 1rem;     /* 16px */
  --space-5: 1.25rem;  /* 20px */
  --space-6: 1.5rem;   /* 24px */
  --space-8: 2rem;     /* 32px */
  --space-10: 2.5rem;  /* 40px */
  --space-12: 3rem;    /* 48px */
  --space-16: 4rem;    /* 64px */
  --space-20: 5rem;    /* 80px */
  --space-24: 6rem;    /* 96px */
  
  /* 圆角 */
  --radius-sm: 0.25rem;   /* 4px */
  --radius-md: 0.375rem;  /* 6px */
  --radius-lg: 0.5rem;    /* 8px */
  --radius-xl: 0.75rem;   /* 12px */
  --radius-2xl: 1rem;     /* 16px */
  --radius-full: 9999px;
}

/* ===== 动画系统 ===== */
:root {
  /* 缓动函数 - 苹果风格 */
  --ease-out: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --ease-in: cubic-bezier(0.55, 0.055, 0.675, 0.19);
  --ease-in-out: cubic-bezier(0.645, 0.045, 0.355, 1);
  --ease-spring: cubic-bezier(0.175, 0.885, 0.32, 1.275);
  
  /* 动画时长 */
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --duration-slow: 350ms;
  --duration-slower: 500ms;
}

/* ===== 基础样式重置 ===== */
* {
  box-sizing: border-box;
}

html {
  font-family: var(--font-system);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

body {
  margin: 0;
  padding: 0;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-size: var(--text-base);
  line-height: var(--leading-normal);
  transition: background-color var(--duration-normal) var(--ease-out),
              color var(--duration-normal) var(--ease-out);
}

/* ===== 实用工具类 ===== */
.apple-card {
  background: var(--bg-elevated);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  transition: all var(--duration-normal) var(--ease-out);
}

.apple-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.apple-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-3) var(--space-6);
  border: none;
  border-radius: var(--radius-lg);
  font-family: var(--font-system);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: all var(--duration-fast) var(--ease-out);
  user-select: none;
}

.apple-button-primary {
  background: var(--apple-blue-500);
  color: white;
}

.apple-button-primary:hover {
  background: var(--apple-blue-600);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.apple-button-secondary {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-primary);
}

.apple-button-secondary:hover {
  background: var(--bg-tertiary);
}

.apple-input {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-family: var(--font-system);
  font-size: var(--text-base);
  transition: all var(--duration-fast) var(--ease-out);
}

.apple-input:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

.apple-glass {
  background: var(--bg-overlay);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}

/* ===== 滚动条样式 ===== */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: var(--apple-gray-300);
  border-radius: var(--radius-full);
  border: 2px solid transparent;
  background-clip: content-box;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--apple-gray-400);
  background-clip: content-box;
}

.dark ::-webkit-scrollbar-thumb {
  background: var(--apple-gray-600);
  background-clip: content-box;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: var(--apple-gray-500);
  background-clip: content-box;
}
