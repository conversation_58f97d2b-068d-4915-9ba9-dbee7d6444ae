import { Router, Request, Response } from 'express';
import { connectionManager } from '../services/RedisConnectionManager';
import { asyncHandler } from '../utils/asyncHandler';
import { ApiResponse } from '../types/api';

const router = Router();

// 获取Redis服务器信息
router.get('/redis/:connectionId/info', asyncHandler(async (req: Request, res: Response) => {
  const { connectionId } = req.params;
  const client = connectionManager.getConnection(connectionId);
  
  if (!client) {
    return res.status(404).json({
      success: false,
      error: 'Connection not found',
    });
  }

  try {
    const info = await client.info();
    const parsedInfo = parseRedisInfo(info);
    
    const response: ApiResponse = {
      success: true,
      data: parsedInfo,
      message: 'Redis info retrieved successfully'
    };
    
    return res.json(response);
  } catch (error: any) {
    const response: ApiResponse = {
      success: false,
      error: error.message,
      message: 'Failed to get Redis info'
    };

    return res.status(500).json(response);
  }
}));

// 获取数据库列表
router.get('/redis/:connectionId/databases', asyncHandler(async (req: Request, res: Response) => {
  const { connectionId } = req.params;
  const client = connectionManager.getConnection(connectionId);
  
  if (!client) {
    return res.status(404).json({
      success: false,
      error: 'Connection not found',
    });
  }

  try {
    const info = await client.info('keyspace');
    const databases = parseKeyspaceInfo(info);
    
    const response: ApiResponse = {
      success: true,
      data: databases,
      message: 'Databases retrieved successfully'
    };
    
    return res.json(response);
  } catch (error: any) {
    const response: ApiResponse = {
      success: false,
      error: error.message,
      message: 'Failed to get databases'
    };

    return res.status(500).json(response);
  }
}));

// 获取键列表
router.get('/redis/:connectionId/keys', asyncHandler(async (req: Request, res: Response) => {
  const { connectionId } = req.params;
  const { pattern = '*', cursor = '0', count = '100', db = '0' } = req.query;
  
  const client = connectionManager.getConnection(connectionId);
  
  if (!client) {
    return res.status(404).json({
      success: false,
      error: 'Connection not found',
    });
  }

  try {
    // 切换到指定数据库
    await client.select(parseInt(db as string));
    
    // 使用SCAN命令获取键
    const result = await client.scan(
      cursor as string,
      'MATCH', pattern as string,
      'COUNT', parseInt(count as string)
    );
    
    const [nextCursor, keys] = result;
    
    // 获取键的详细信息
    const keyInfos = await Promise.all(
      keys.map(async (key) => {
        try {
          const [type, ttl, size] = await Promise.all([
            client.type(key),
            client.ttl(key),
            getKeySize(client, key)
          ]);
          
          return {
            key,
            type,
            ttl: ttl === -1 ? null : ttl,
            size,
          };
        } catch (error) {
          return {
            key,
            type: 'unknown',
            ttl: null,
            size: 0,
            error: (error as Error).message,
          };
        }
      })
    );
    
    const response: ApiResponse = {
      success: true,
      data: {
        cursor: nextCursor,
        keys: keyInfos,
        hasMore: nextCursor !== '0',
      },
      message: 'Keys retrieved successfully'
    };
    
    return res.json(response);
  } catch (error: any) {
    const response: ApiResponse = {
      success: false,
      error: error.message,
      message: 'Failed to get keys'
    };

    return res.status(500).json(response);
  }
}));

// 获取键值
router.get('/redis/:connectionId/key/:key', asyncHandler(async (req: Request, res: Response) => {
  const { connectionId, key } = req.params;
  const { db = '0' } = req.query;
  
  const client = connectionManager.getConnection(connectionId);
  
  if (!client) {
    return res.status(404).json({
      success: false,
      error: 'Connection not found',
    });
  }

  try {
    // 切换到指定数据库
    await client.select(parseInt(db as string));
    
    // 检查键是否存在
    const exists = await client.exists(key);
    if (!exists) {
      return res.status(404).json({
        success: false,
        error: 'Key not found',
      });
    }
    
    // 获取键类型和TTL
    const [type, ttl] = await Promise.all([
      client.type(key),
      client.ttl(key)
    ]);
    
    let value: any;
    
    // 根据类型获取值
    switch (type) {
      case 'string':
        value = await client.get(key);
        break;
      case 'hash':
        value = await client.hgetall(key);
        break;
      case 'list':
        value = await client.lrange(key, 0, -1);
        break;
      case 'set':
        value = await client.smembers(key);
        break;
      case 'zset':
        value = await client.zrange(key, 0, -1, 'WITHSCORES');
        break;
      default:
        value = null;
    }
    
    const response: ApiResponse = {
      success: true,
      data: {
        key,
        type,
        value,
        ttl: ttl === -1 ? null : ttl,
      },
      message: 'Key value retrieved successfully'
    };
    
    return res.json(response);
  } catch (error: any) {
    const response: ApiResponse = {
      success: false,
      error: error.message,
      message: 'Failed to get key value'
    };

    return res.status(500).json(response);
  }
}));

// 执行Redis命令
router.post('/redis/:connectionId/command', asyncHandler(async (req: Request, res: Response) => {
  const { connectionId } = req.params;
  const { command, args = [], db = 0 } = req.body;
  
  const client = connectionManager.getConnection(connectionId);
  
  if (!client) {
    return res.status(404).json({
      success: false,
      error: 'Connection not found',
    });
  }

  if (!command) {
    return res.status(400).json({
      success: false,
      error: 'Command is required',
    });
  }

  try {
    // 切换到指定数据库
    await client.select(db);
    
    // 执行命令
    const result = await client.call(command.toUpperCase(), ...args);
    
    const response: ApiResponse = {
      success: true,
      data: { result },
      message: 'Command executed successfully'
    };
    
    return res.json(response);
  } catch (error: any) {
    const response: ApiResponse = {
      success: false,
      error: error.message,
      message: 'Failed to execute command'
    };

    return res.status(500).json(response);
  }
}));

// 辅助函数：解析Redis INFO命令输出
function parseRedisInfo(info: string): Record<string, any> {
  const result: Record<string, any> = {};
  const sections = info.split('\r\n\r\n');
  
  sections.forEach(section => {
    const lines = section.split('\r\n');
    let currentSection = '';
    
    lines.forEach(line => {
      if (line.startsWith('# ')) {
        currentSection = line.substring(2).toLowerCase();
        result[currentSection] = {};
      } else if (line.includes(':') && currentSection) {
        const [key, value] = line.split(':');
        result[currentSection][key] = isNaN(Number(value)) ? value : Number(value);
      }
    });
  });
  
  return result;
}

// 辅助函数：解析keyspace信息
function parseKeyspaceInfo(info: string): Array<{ db: number; keys: number; expires: number }> {
  const databases: Array<{ db: number; keys: number; expires: number }> = [];
  const lines = info.split('\r\n');
  
  lines.forEach(line => {
    if (line.startsWith('db')) {
      const match = line.match(/db(\d+):keys=(\d+),expires=(\d+)/);
      if (match) {
        databases.push({
          db: parseInt(match[1]),
          keys: parseInt(match[2]),
          expires: parseInt(match[3]),
        });
      }
    }
  });
  
  return databases;
}

// 辅助函数：获取键大小
async function getKeySize(client: any, key: string): Promise<number> {
  try {
    const type = await client.type(key);
    
    switch (type) {
      case 'string':
        return await client.strlen(key);
      case 'hash':
        return await client.hlen(key);
      case 'list':
        return await client.llen(key);
      case 'set':
        return await client.scard(key);
      case 'zset':
        return await client.zcard(key);
      default:
        return 0;
    }
  } catch (error) {
    return 0;
  }
}

// 获取Redis性能统计信息
router.get('/:connectionId/stats', asyncHandler(async (req: Request, res: Response) => {
  const { connectionId } = req.params;

  const connection = connectionManager.getConnection(connectionId);
  if (!connection) {
    return res.status(404).json({
      success: false,
      error: 'Connection not found',
    });
  }

  const { client } = connection;

  try {
    // 获取Redis INFO信息
    const info = await (client as any).info();
    const infoData = parseRedisInfo(info);

    // 获取慢查询日志
    let slowlog: any[] = [];
    try {
      slowlog = await (client as any).slowlog('get', 10);
    } catch (error) {
      console.warn('Failed to get slowlog:', error);
    }

    // 获取客户端连接信息
    let clientCount = 0;
    try {
      const clientList = await (client as any).client('list');
      clientCount = clientList.split('\n').filter((line: string) => line.trim()).length;
    } catch (error) {
      console.warn('Failed to get client list:', error);
    }

    // 构建性能统计数据
    const stats = {
      server: {
        version: infoData.server?.redis_version || 'unknown',
        uptime: infoData.server?.uptime_in_seconds || 0,
        mode: infoData.server?.redis_mode || 'standalone',
      },
      memory: {
        used: infoData.memory?.used_memory || 0,
        used_human: infoData.memory?.used_memory_human || '0B',
        peak: infoData.memory?.used_memory_peak || 0,
        peak_human: infoData.memory?.used_memory_peak_human || '0B',
        rss: infoData.memory?.used_memory_rss || 0,
        rss_human: infoData.memory?.used_memory_rss_human || '0B',
        fragmentation_ratio: infoData.memory?.mem_fragmentation_ratio || 0,
      },
      clients: {
        connected: infoData.clients?.connected_clients || 0,
        blocked: infoData.clients?.blocked_clients || 0,
        total: clientCount,
      },
      stats: {
        total_connections_received: infoData.stats?.total_connections_received || 0,
        total_commands_processed: infoData.stats?.total_commands_processed || 0,
        instantaneous_ops_per_sec: infoData.stats?.instantaneous_ops_per_sec || 0,
        keyspace_hits: infoData.stats?.keyspace_hits || 0,
        keyspace_misses: infoData.stats?.keyspace_misses || 0,
        hit_rate: infoData.stats?.keyspace_hits && infoData.stats?.keyspace_misses
          ? ((infoData.stats.keyspace_hits / (infoData.stats.keyspace_hits + infoData.stats.keyspace_misses)) * 100).toFixed(2)
          : '0.00',
      },
      cpu: {
        used_cpu_sys: infoData.cpu?.used_cpu_sys || 0,
        used_cpu_user: infoData.cpu?.used_cpu_user || 0,
        used_cpu_sys_children: infoData.cpu?.used_cpu_sys_children || 0,
        used_cpu_user_children: infoData.cpu?.used_cpu_user_children || 0,
      },
      keyspace: infoData.keyspace || {},
      slowlog: Array.isArray(slowlog) ? slowlog.map((entry: any) => ({
        id: entry[0],
        timestamp: entry[1],
        duration: entry[2],
        command: entry[3],
      })) : [],
    };

    const response: ApiResponse = {
      success: true,
      data: stats,
      message: 'Redis stats retrieved successfully'
    };

    return res.json(response);
  } catch (error: any) {
    const response: ApiResponse = {
      success: false,
      error: error.message,
      message: 'Failed to get Redis stats'
    };

    return res.status(500).json(response);
  }
}));

// 导出Redis数据
router.get('/:connectionId/export', asyncHandler(async (req: Request, res: Response) => {
  const { connectionId } = req.params;
  const { format = 'json', pattern = '*', db = 0 } = req.query;

  const connection = connectionManager.getConnection(connectionId);
  if (!connection) {
    return res.status(404).json({
      success: false,
      error: 'Connection not found',
    });
  }

  const { client } = connection;

  try {
    // 切换到指定数据库
    await (client as any).select(Number(db));

    // 获取所有匹配的键
    const keys = await (client as any).keys(pattern as string);
    const exportData: any[] = [];

    // 批量获取键值数据
    for (const key of keys) {
      try {
        const type = await (client as any).type(key);
        const ttl = await (client as any).ttl(key);
        let value: any;

        switch (type) {
          case 'string':
            value = await (client as any).get(key);
            break;
          case 'hash':
            value = await (client as any).hgetall(key);
            break;
          case 'list':
            value = await (client as any).lrange(key, 0, -1);
            break;
          case 'set':
            value = await (client as any).smembers(key);
            break;
          case 'zset':
            value = await (client as any).zrange(key, 0, -1, 'WITHSCORES');
            break;
          default:
            continue;
        }

        exportData.push({
          key,
          type,
          value,
          ttl: ttl === -1 ? null : ttl,
        });
      } catch (error) {
        console.warn(`Failed to export key ${key}:`, error);
      }
    }

    // 根据格式返回数据
    if (format === 'csv') {
      // CSV格式
      const csvHeader = 'key,type,value,ttl\n';
      const csvRows = exportData.map(item => {
        const value = typeof item.value === 'object'
          ? JSON.stringify(item.value).replace(/"/g, '""')
          : String(item.value).replace(/"/g, '""');
        return `"${item.key}","${item.type}","${value}","${item.ttl || ''}"`;
      }).join('\n');

      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename="redis-export-${Date.now()}.csv"`);
      return res.send(csvHeader + csvRows);
    } else {
      // JSON格式
      const jsonData = {
        metadata: {
          exportTime: new Date().toISOString(),
          database: Number(db),
          pattern: pattern as string,
          totalKeys: exportData.length,
        },
        data: exportData,
      };

      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Content-Disposition', `attachment; filename="redis-export-${Date.now()}.json"`);
      return res.json(jsonData);
    }
  } catch (error: any) {
    const response: ApiResponse = {
      success: false,
      error: error.message,
      message: 'Failed to export Redis data'
    };

    return res.status(500).json(response);
  }
}));

// 导入Redis数据
router.post('/:connectionId/import', asyncHandler(async (req: Request, res: Response) => {
  const { connectionId } = req.params;
  const { data, db = 0, overwrite = false } = req.body;

  const connection = connectionManager.getConnection(connectionId);
  if (!connection) {
    return res.status(404).json({
      success: false,
      error: 'Connection not found',
    });
  }

  const { client } = connection;

  try {
    // 切换到指定数据库
    await (client as any).select(Number(db));

    let importData: any[];

    // 解析导入数据
    if (Array.isArray(data)) {
      importData = data;
    } else if (data.data && Array.isArray(data.data)) {
      importData = data.data;
    } else {
      throw new Error('Invalid import data format');
    }

    const results = {
      total: importData.length,
      success: 0,
      failed: 0,
      skipped: 0,
      errors: [] as string[],
    };

    // 批量导入数据
    for (const item of importData) {
      try {
        const { key, type, value, ttl } = item;

        // 检查键是否存在
        if (!overwrite && await (client as any).exists(key)) {
          results.skipped++;
          continue;
        }

        // 根据类型设置值
        switch (type) {
          case 'string':
            await (client as any).set(key, value);
            break;
          case 'hash':
            await (client as any).hset(key, value);
            break;
          case 'list':
            await (client as any).del(key);
            if (Array.isArray(value) && value.length > 0) {
              await (client as any).lpush(key, ...value.reverse());
            }
            break;
          case 'set':
            await (client as any).del(key);
            if (Array.isArray(value) && value.length > 0) {
              await (client as any).sadd(key, ...value);
            }
            break;
          case 'zset':
            await (client as any).del(key);
            if (Array.isArray(value) && value.length > 0) {
              const args = [];
              for (let i = 0; i < value.length; i += 2) {
                args.push(value[i + 1], value[i]); // score, member
              }
              if (args.length > 0) {
                await (client as any).zadd(key, ...args);
              }
            }
            break;
          default:
            results.errors.push(`Unsupported type ${type} for key ${key}`);
            results.failed++;
            continue;
        }

        // 设置TTL
        if (ttl && ttl > 0) {
          await (client as any).expire(key, ttl);
        }

        results.success++;
      } catch (error: any) {
        results.failed++;
        results.errors.push(`Failed to import key ${item.key}: ${error.message}`);
      }
    }

    const response: ApiResponse = {
      success: true,
      data: results,
      message: `Import completed: ${results.success} success, ${results.failed} failed, ${results.skipped} skipped`
    };

    return res.json(response);
  } catch (error: any) {
    const response: ApiResponse = {
      success: false,
      error: error.message,
      message: 'Failed to import Redis data'
    };

    return res.status(500).json(response);
  }
}));

export default router;
