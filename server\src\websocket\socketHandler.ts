import { Server, Socket } from 'socket.io';
import { logger } from '../utils/logger';
import { connectionManager } from '../services/RedisConnectionManager';

interface ClientInfo {
  id: string;
  connectionId?: string;
  connectedAt: Date;
}

class WebSocketManager {
  private io: Server;
  private clients: Map<string, ClientInfo> = new Map();
  private connectionMonitors: Map<string, NodeJS.Timeout> = new Map();

  constructor(io: Server) {
    this.io = io;
  }

  public setupEventHandlers() {
    this.io.on('connection', (socket: Socket) => {
      logger.info(`WebSocket client connected: ${socket.id}`);
      
      // 注册客户端
      this.clients.set(socket.id, {
        id: socket.id,
        connectedAt: new Date(),
      });

      // 发送连接确认
      socket.emit('connected', {
        clientId: socket.id,
        timestamp: new Date().toISOString(),
      });

      // 监听连接选择事件
      socket.on('subscribe-connection', (connectionId: string) => {
        this.handleConnectionSubscription(socket, connectionId);
      });

      // 监听取消连接订阅事件
      socket.on('unsubscribe-connection', () => {
        this.handleConnectionUnsubscription(socket);
      });

      // 监听实时数据请求
      socket.on('request-realtime-data', (data: { connectionId: string; type: string }) => {
        this.handleRealtimeDataRequest(socket, data);
      });

      // 监听断开连接事件
      socket.on('disconnect', () => {
        logger.info(`WebSocket client disconnected: ${socket.id}`);
        this.handleClientDisconnect(socket.id);
      });

      // 错误处理
      socket.on('error', (error) => {
        logger.error(`WebSocket error for client ${socket.id}:`, error);
      });
    });
  }

  private handleConnectionSubscription(socket: Socket, connectionId: string) {
    const clientInfo = this.clients.get(socket.id);
    if (!clientInfo) return;

    // 取消之前的订阅
    this.handleConnectionUnsubscription(socket);

    // 更新客户端信息
    clientInfo.connectionId = connectionId;
    this.clients.set(socket.id, clientInfo);

    // 加入连接房间
    socket.join(`connection:${connectionId}`);

    // 开始监控连接状态
    this.startConnectionMonitoring(connectionId);

    logger.info(`Client ${socket.id} subscribed to connection ${connectionId}`);

    // 发送初始连接状态
    this.sendConnectionStatus(connectionId);
  }

  private handleConnectionUnsubscription(socket: Socket) {
    const clientInfo = this.clients.get(socket.id);
    if (!clientInfo || !clientInfo.connectionId) return;

    const connectionId = clientInfo.connectionId;

    // 离开连接房间
    socket.leave(`connection:${connectionId}`);

    // 更新客户端信息
    clientInfo.connectionId = undefined;
    this.clients.set(socket.id, clientInfo);

    // 检查是否还有其他客户端订阅此连接
    const hasOtherSubscribers = Array.from(this.clients.values())
      .some(client => client.connectionId === connectionId && client.id !== socket.id);

    // 如果没有其他订阅者，停止监控
    if (!hasOtherSubscribers) {
      this.stopConnectionMonitoring(connectionId);
    }

    logger.info(`Client ${socket.id} unsubscribed from connection ${connectionId}`);
  }

  private handleRealtimeDataRequest(socket: Socket, data: { connectionId: string; type: string }) {
    const { connectionId, type } = data;

    // 验证连接是否存在
    const connectionInfo = connectionManager.getConnectionInfo(connectionId);
    if (!connectionInfo) {
      socket.emit('error', { message: 'Connection not found' });
      return;
    }

    // 根据类型发送相应数据
    switch (type) {
      case 'stats':
        this.sendConnectionStats(connectionId);
        break;
      case 'info':
        this.sendConnectionInfo(connectionId);
        break;
      default:
        socket.emit('error', { message: 'Unknown data type' });
    }
  }

  private handleClientDisconnect(clientId: string) {
    const clientInfo = this.clients.get(clientId);
    if (!clientInfo) return;

    // 如果客户端订阅了连接，处理取消订阅
    if (clientInfo.connectionId) {
      const connectionId = clientInfo.connectionId;
      
      // 检查是否还有其他客户端订阅此连接
      const hasOtherSubscribers = Array.from(this.clients.values())
        .some(client => client.connectionId === connectionId && client.id !== clientId);

      // 如果没有其他订阅者，停止监控
      if (!hasOtherSubscribers) {
        this.stopConnectionMonitoring(connectionId);
      }
    }

    // 移除客户端信息
    this.clients.delete(clientId);
  }

  private startConnectionMonitoring(connectionId: string) {
    // 如果已经在监控，先停止
    this.stopConnectionMonitoring(connectionId);

    // 每5秒检查一次连接状态
    const interval = setInterval(() => {
      this.checkConnectionStatus(connectionId);
    }, 5000);

    this.connectionMonitors.set(connectionId, interval);
    logger.info(`Started monitoring connection ${connectionId}`);
  }

  private stopConnectionMonitoring(connectionId: string) {
    const interval = this.connectionMonitors.get(connectionId);
    if (interval) {
      clearInterval(interval);
      this.connectionMonitors.delete(connectionId);
      logger.info(`Stopped monitoring connection ${connectionId}`);
    }
  }

  private async checkConnectionStatus(connectionId: string) {
    try {
      const connectionInfo = connectionManager.getConnectionInfo(connectionId);
      if (!connectionInfo) {
        // 连接不存在，通知客户端
        this.io.to(`connection:${connectionId}`).emit('connection-status', {
          connectionId,
          status: 'disconnected',
          timestamp: new Date().toISOString(),
        });
        this.stopConnectionMonitoring(connectionId);
        return;
      }

      // 测试连接
      const { client } = connectionInfo;
      await (client as any).ping();

      // 连接正常
      this.io.to(`connection:${connectionId}`).emit('connection-status', {
        connectionId,
        status: 'connected',
        timestamp: new Date().toISOString(),
      });

    } catch (error) {
      // 连接异常
      this.io.to(`connection:${connectionId}`).emit('connection-status', {
        connectionId,
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      });
    }
  }

  private async sendConnectionStatus(connectionId: string) {
    try {
      const connectionInfo = connectionManager.getConnectionInfo(connectionId);
      if (!connectionInfo) {
        this.io.to(`connection:${connectionId}`).emit('connection-status', {
          connectionId,
          status: 'disconnected',
          timestamp: new Date().toISOString(),
        });
        return;
      }

      // 测试连接
      const { client } = connectionInfo;
      await (client as any).ping();

      this.io.to(`connection:${connectionId}`).emit('connection-status', {
        connectionId,
        status: 'connected',
        timestamp: new Date().toISOString(),
      });

    } catch (error) {
      this.io.to(`connection:${connectionId}`).emit('connection-status', {
        connectionId,
        status: 'error',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      });
    }
  }

  private async sendConnectionStats(connectionId: string) {
    try {
      const connectionInfo = connectionManager.getConnectionInfo(connectionId);
      if (!connectionInfo) return;

      const { client } = connectionInfo;
      const info = await (client as any).info();
      
      // 解析基本统计信息
      const lines = info.split('\r\n');
      const stats: any = {};
      
      for (const line of lines) {
        if (line.includes(':')) {
          const [key, value] = line.split(':');
          stats[key] = value;
        }
      }

      this.io.to(`connection:${connectionId}`).emit('realtime-stats', {
        connectionId,
        stats: {
          connected_clients: stats.connected_clients || '0',
          used_memory_human: stats.used_memory_human || '0B',
          instantaneous_ops_per_sec: stats.instantaneous_ops_per_sec || '0',
          keyspace_hits: stats.keyspace_hits || '0',
          keyspace_misses: stats.keyspace_misses || '0',
        },
        timestamp: new Date().toISOString(),
      });

    } catch (error) {
      logger.error(`Failed to send connection stats for ${connectionId}:`, error);
    }
  }

  private async sendConnectionInfo(connectionId: string) {
    try {
      const connectionInfo = connectionManager.getConnectionInfo(connectionId);
      if (!connectionInfo) return;

      const { config } = connectionInfo;

      this.io.to(`connection:${connectionId}`).emit('connection-info', {
        connectionId,
        info: {
          name: config.name,
          host: config.host,
          port: config.port,
          database: config.database || 0,
        },
        timestamp: new Date().toISOString(),
      });

    } catch (error) {
      logger.error(`Failed to send connection info for ${connectionId}:`, error);
    }
  }

  // 广播消息给所有连接的客户端
  public broadcast(event: string, data: any) {
    this.io.emit(event, data);
  }

  // 发送消息给特定连接的订阅者
  public sendToConnection(connectionId: string, event: string, data: any) {
    this.io.to(`connection:${connectionId}`).emit(event, data);
  }

  // 获取连接的客户端数量
  public getConnectionSubscribers(connectionId: string): number {
    return Array.from(this.clients.values())
      .filter(client => client.connectionId === connectionId).length;
  }

  // 获取总客户端数量
  public getTotalClients(): number {
    return this.clients.size;
  }
}

let wsManager: WebSocketManager;

export function setupWebSocket(io: Server) {
  wsManager = new WebSocketManager(io);
  wsManager.setupEventHandlers();
  logger.info('WebSocket handlers setup completed');
}

export function getWebSocketManager(): WebSocketManager {
  return wsManager;
}
