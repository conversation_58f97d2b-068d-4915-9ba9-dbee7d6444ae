import React, { useState, useRef, useEffect } from 'react';
import {
  Input,
  Button,
  Card,
  Typography,
  Space,
  Tag,
  List,
  message,
  Tooltip,
} from 'antd';
import {
  PlayCircleOutlined,
  ClearOutlined,
  HistoryOutlined,
  CodeOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CopyOutlined,
} from '@ant-design/icons';
import { useConnectionStore } from '../../stores/connectionStore';
import { useRedisStore } from '../../stores/redisStore';
import { ApiService } from '../../services/api';

const { TextArea } = Input;
const { Text, Title } = Typography;

interface CommandHistory {
  id: string;
  command: string;
  args: string[];
  result: any;
  timestamp: Date;
  success: boolean;
  error?: string;
  duration?: number;
}

const AppleCommandExecutor: React.FC = () => {
  const [command, setCommand] = useState('');
  const [executing, setExecuting] = useState(false);
  const [history, setHistory] = useState<CommandHistory[]>([]);
  const inputRef = useRef<any>(null);
  
  const { activeConnectionId } = useConnectionStore();
  const { currentDatabase } = useRedisStore();

  // 从localStorage加载历史记录
  useEffect(() => {
    const savedHistory = localStorage.getItem('redis-command-history');
    if (savedHistory) {
      try {
        const parsed = JSON.parse(savedHistory);
        setHistory(parsed.map((item: any) => ({
          ...item,
          timestamp: new Date(item.timestamp),
        })));
      } catch (error) {
        console.error('Failed to load command history:', error);
      }
    }
  }, []);

  // 保存历史记录到localStorage
  const saveHistory = (newHistory: CommandHistory[]) => {
    setHistory(newHistory);
    localStorage.setItem('redis-command-history', JSON.stringify(newHistory));
  };

  // 执行Redis命令
  const executeCommand = async (connectionId: string, command: string, args: string[]) => {
    const response = await ApiService.executeCommand(connectionId, command, args);
    if (!response.success) {
      throw new Error(response.error || 'Command execution failed');
    }
    return response.data;
  };

  // 处理命令执行
  const handleExecute = async () => {
    if (!command.trim() || !activeConnectionId) return;

    const startTime = Date.now();
    setExecuting(true);

    try {
      const parts = command.trim().split(/\s+/);
      const cmd = parts[0].toUpperCase();
      const args = parts.slice(1);

      const result = await executeCommand(activeConnectionId, cmd, args);
      const duration = Date.now() - startTime;
      
      const historyItem: CommandHistory = {
        id: Date.now().toString(),
        command: cmd,
        args,
        result,
        timestamp: new Date(),
        success: true,
        duration,
      };

      const newHistory = [historyItem, ...history.slice(0, 99)]; // 保留最近100条
      saveHistory(newHistory);
      
      message.success(`命令执行成功 (${duration}ms)`);
      setCommand('');
    } catch (error: any) {
      const duration = Date.now() - startTime;
      const historyItem: CommandHistory = {
        id: Date.now().toString(),
        command: command.trim().split(/\s+/)[0].toUpperCase(),
        args: command.trim().split(/\s+/).slice(1),
        result: null,
        timestamp: new Date(),
        success: false,
        error: error.message,
        duration,
      };

      const newHistory = [historyItem, ...history.slice(0, 99)];
      saveHistory(newHistory);
      
      message.error(`命令执行失败: ${error.message}`);
    } finally {
      setExecuting(false);
    }
  };

  // 处理键盘快捷键
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.ctrlKey && e.key === 'Enter') {
      e.preventDefault();
      handleExecute();
    }
  };

  // 清空历史记录
  const handleClearHistory = () => {
    setHistory([]);
    localStorage.removeItem('redis-command-history');
    message.success('历史记录已清空');
  };

  // 复制命令到输入框
  const handleCopyCommand = (historyItem: CommandHistory) => {
    const fullCommand = [historyItem.command, ...historyItem.args].join(' ');
    setCommand(fullCommand);
    inputRef.current?.focus();
  };

  // 格式化结果显示
  const formatResult = (result: any) => {
    if (result === null || result === undefined) {
      return '(nil)';
    }
    if (typeof result === 'string') {
      return result;
    }
    if (Array.isArray(result)) {
      return result.map((item, index) => `${index + 1}) ${item}`).join('\n');
    }
    return JSON.stringify(result, null, 2);
  };

  // 格式化时间
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('zh-CN', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  };

  if (!activeConnectionId) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-[var(--bg-secondary)] rounded-2xl flex items-center justify-center mx-auto mb-4">
            <CodeOutlined className="text-2xl text-[var(--text-tertiary)]" />
          </div>
          <Title level={4} className="text-[var(--text-secondary)] mb-2">
            请先选择一个Redis连接
          </Title>
          <Text className="text-[var(--text-tertiary)]">
            连接到Redis服务器后即可执行命令
          </Text>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col p-6 space-y-6">
      {/* 命令输入区 */}
      <Card className="apple-card">
        <div className="mb-4">
          <div className="flex items-center justify-between mb-3">
            <Title level={4} className="mb-0 text-[var(--text-primary)]">
              <CodeOutlined className="mr-2" />
              Redis命令执行器
            </Title>
            <Space>
              <Tag color="blue" className="text-xs">
                DB {currentDatabase}
              </Tag>
              <Tag color="green" className="text-xs">
                Ctrl+Enter 执行
              </Tag>
            </Space>
          </div>
        </div>

        <div className="mb-4">
          <TextArea
            ref={inputRef}
            value={command}
            onChange={(e) => setCommand(e.target.value)}
            onKeyDown={handleKeyPress}
            placeholder="输入Redis命令，例如: GET mykey"
            rows={3}
            className="font-mono apple-input"
            style={{ fontFamily: 'var(--font-mono)' }}
          />
        </div>

        <div className="flex justify-between">
          <Space>
            <Button
              type="primary"
              icon={<PlayCircleOutlined />}
              onClick={handleExecute}
              loading={executing}
              disabled={!command.trim()}
              className="apple-button apple-button-primary"
            >
              执行命令
            </Button>
            <Button
              icon={<ClearOutlined />}
              onClick={() => setCommand('')}
              disabled={!command.trim()}
              className="apple-button apple-button-secondary"
            >
              清空
            </Button>
          </Space>
          
          <Button
            icon={<ClearOutlined />}
            onClick={handleClearHistory}
            disabled={history.length === 0}
            className="apple-button apple-button-secondary"
          >
            清空历史
          </Button>
        </div>
      </Card>

      {/* 执行历史 */}
      <Card
        className="apple-card flex-1 overflow-hidden"
        title={
          <Space>
            <HistoryOutlined />
            <span>执行历史</span>
            <Tag>{history.length}</Tag>
          </Space>
        }
      >
        <div className="h-full overflow-auto">
          {history.length === 0 ? (
            <div className="h-full flex items-center justify-center">
              <div className="text-center">
                <div className="w-12 h-12 bg-[var(--bg-secondary)] rounded-xl flex items-center justify-center mx-auto mb-3">
                  <HistoryOutlined className="text-lg text-[var(--text-tertiary)]" />
                </div>
                <Text className="text-[var(--text-secondary)]">
                  暂无执行历史
                </Text>
              </div>
            </div>
          ) : (
            <List
              dataSource={history}
              renderItem={(item) => (
                <List.Item className="border-b border-[var(--border-secondary)] last:border-b-0">
                  <div className="w-full">
                    {/* 命令头部 */}
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <div
                          className={`
                            w-6 h-6 rounded-full flex items-center justify-center text-xs
                            ${item.success 
                              ? 'bg-[var(--apple-success)]15 text-[var(--apple-success)]' 
                              : 'bg-[var(--apple-error)]15 text-[var(--apple-error)]'
                            }
                          `}
                        >
                          {item.success ? <CheckCircleOutlined /> : <ExclamationCircleOutlined />}
                        </div>
                        <Text
                          className="font-mono font-medium text-[var(--text-primary)]"
                          style={{ fontFamily: 'var(--font-mono)' }}
                        >
                          {[item.command, ...item.args].join(' ')}
                        </Text>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <Text className="text-[var(--text-tertiary)] text-xs">
                          <ClockCircleOutlined className="mr-1" />
                          {formatTime(item.timestamp)}
                        </Text>
                        {item.duration && (
                          <Text className="text-[var(--text-tertiary)] text-xs">
                            {item.duration}ms
                          </Text>
                        )}
                        <Tooltip title="复制命令">
                          <Button
                            type="text"
                            size="small"
                            icon={<CopyOutlined />}
                            onClick={() => handleCopyCommand(item)}
                            className="text-[var(--text-secondary)] hover:text-[var(--text-primary)]"
                          />
                        </Tooltip>
                      </div>
                    </div>

                    {/* 结果显示 */}
                    <div className="ml-8">
                      {item.success ? (
                        <pre className="bg-[var(--bg-secondary)] p-3 rounded-lg text-sm text-[var(--text-primary)] whitespace-pre-wrap font-mono overflow-x-auto">
                          {formatResult(item.result)}
                        </pre>
                      ) : (
                        <div className="bg-[var(--apple-error)]10 border border-[var(--apple-error)]30 p-3 rounded-lg">
                          <Text className="text-[var(--apple-error)] text-sm">
                            错误: {item.error}
                          </Text>
                        </div>
                      )}
                    </div>
                  </div>
                </List.Item>
              )}
            />
          )}
        </div>
      </Card>
    </div>
  );
};

export default AppleCommandExecutor;
