/* Apple-Inspired Animation System */

/* ===== 基础动画类 ===== */
.apple-fade-in {
  animation: appleSlideUp var(--duration-normal) var(--ease-out) forwards;
  opacity: 0;
  transform: translateY(20px);
}

.apple-fade-in-delay-1 {
  animation-delay: 100ms;
}

.apple-fade-in-delay-2 {
  animation-delay: 200ms;
}

.apple-fade-in-delay-3 {
  animation-delay: 300ms;
}

/* ===== 关键帧动画 ===== */
@keyframes appleSlideUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes appleSlideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes appleSlideLeft {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes appleSlideRight {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes appleScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes applePulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes appleShake {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-4px);
  }
  75% {
    transform: translateX(4px);
  }
}

/* ===== 页面转场动画 ===== */
.apple-page-enter {
  animation: appleSlideLeft var(--duration-normal) var(--ease-out);
}

.apple-page-exit {
  animation: appleSlideRight var(--duration-normal) var(--ease-out);
}

/* ===== 模态框动画 ===== */
.apple-modal-enter {
  animation: appleModalEnter var(--duration-normal) var(--ease-out);
}

.apple-modal-exit {
  animation: appleModalExit var(--duration-fast) var(--ease-in);
}

@keyframes appleModalEnter {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes appleModalExit {
  from {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
  to {
    opacity: 0;
    transform: scale(0.95) translateY(-20px);
  }
}

/* ===== 按钮动画 ===== */
.apple-button-hover {
  transition: all var(--duration-fast) var(--ease-out);
}

.apple-button-hover:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.apple-button-hover:active {
  transform: translateY(0);
  transition-duration: 50ms;
}

/* ===== 卡片动画 ===== */
.apple-card-hover {
  transition: all var(--duration-normal) var(--ease-out);
}

.apple-card-hover:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

/* ===== 列表项动画 ===== */
.apple-list-item {
  transition: all var(--duration-fast) var(--ease-out);
}

.apple-list-item:hover {
  transform: translateX(4px);
  background-color: var(--bg-tertiary);
}

/* ===== 加载动画 ===== */
.apple-loading {
  animation: applePulse 2s ease-in-out infinite;
}

.apple-skeleton {
  background: linear-gradient(
    90deg,
    var(--bg-secondary) 25%,
    var(--bg-tertiary) 50%,
    var(--bg-secondary) 75%
  );
  background-size: 200% 100%;
  animation: appleSkeletonLoading 1.5s ease-in-out infinite;
}

@keyframes appleSkeletonLoading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* ===== 状态指示器动画 ===== */
.apple-status-connected {
  animation: applePulse 2s ease-in-out infinite;
  background-color: var(--apple-success);
}

.apple-status-error {
  animation: appleShake 0.5s ease-in-out;
  background-color: var(--apple-error);
}

.apple-status-warning {
  animation: applePulse 1s ease-in-out infinite;
  background-color: var(--apple-warning);
}

/* ===== 通知动画 ===== */
.apple-notification-enter {
  animation: appleNotificationSlideIn var(--duration-normal) var(--ease-spring);
}

.apple-notification-exit {
  animation: appleNotificationSlideOut var(--duration-fast) var(--ease-in);
}

@keyframes appleNotificationSlideIn {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes appleNotificationSlideOut {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(100%);
  }
}

/* ===== 工具提示动画 ===== */
.apple-tooltip {
  animation: appleTooltipShow var(--duration-fast) var(--ease-out);
}

@keyframes appleTooltipShow {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* ===== 焦点动画 ===== */
.apple-focus-ring {
  position: relative;
}

.apple-focus-ring:focus-within::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px solid var(--apple-blue-500);
  border-radius: inherit;
  opacity: 0;
  animation: appleFocusRing var(--duration-fast) var(--ease-out) forwards;
}

@keyframes appleFocusRing {
  to {
    opacity: 0.3;
  }
}

/* ===== 响应式动画控制 ===== */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* ===== 实用工具类 ===== */
.apple-animate-none {
  animation: none !important;
  transition: none !important;
}

.apple-animate-fast {
  animation-duration: var(--duration-fast) !important;
  transition-duration: var(--duration-fast) !important;
}

.apple-animate-slow {
  animation-duration: var(--duration-slow) !important;
  transition-duration: var(--duration-slow) !important;
}

/* ===== 页面级动画 ===== */
.apple-page-container {
  animation: applePageLoad var(--duration-slow) var(--ease-out);
}

@keyframes applePageLoad {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== 交互反馈动画 ===== */
.apple-ripple {
  position: relative;
  overflow: hidden;
}

.apple-ripple::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width var(--duration-normal) var(--ease-out),
              height var(--duration-normal) var(--ease-out);
}

.apple-ripple:active::after {
  width: 200px;
  height: 200px;
}
