import { Server } from 'socket.io';
declare class WebSocketManager {
    private io;
    private clients;
    private connectionMonitors;
    constructor(io: Server);
    setupEventHandlers(): void;
    private handleConnectionSubscription;
    private handleConnectionUnsubscription;
    private handleRealtimeDataRequest;
    private handleClientDisconnect;
    private startConnectionMonitoring;
    private stopConnectionMonitoring;
    private checkConnectionStatus;
    private sendConnectionStatus;
    private sendConnectionStats;
    private sendConnectionInfo;
    broadcast(event: string, data: any): void;
    sendToConnection(connectionId: string, event: string, data: any): void;
    getConnectionSubscribers(connectionId: string): number;
    getTotalClients(): number;
}
export declare function setupWebSocket(io: Server): void;
export declare function getWebSocketManager(): WebSocketManager;
export {};
//# sourceMappingURL=socketHandler.d.ts.map