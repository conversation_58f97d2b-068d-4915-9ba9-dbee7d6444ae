import { useHotkeys } from 'react-hotkeys-hook'
import { useNavigate } from 'react-router-dom'
import { message } from 'antd'
import { useTheme } from '../stores/themeStore'
import { useConnectionStore } from '../stores/connectionStore'

// 临时常量定义
const KEYBOARD_SHORTCUTS = {
  GLOBAL: {
    SEARCH: 'Ctrl+K',
    REFRESH: 'F5',
    NEW_CONNECTION: 'Ctrl+N',
    TOGGLE_THEME: 'Ctrl+Shift+T',
    HELP: 'F1',
    COMMAND_PALETTE: 'Ctrl+Shift+P',
  },
  DATA: {
    NEW_KEY: 'Ctrl+Shift+N',
    DELETE_KEY: 'Delete',
    EDIT_KEY: 'F2',
    COPY_KEY: 'Ctrl+C',
    PASTE_VALUE: 'Ctrl+V',
    UNDO: 'Ctrl+Z',
    REDO: 'Ctrl+Y',
  },
  NAVIGATION: {
    NEXT_TAB: 'Ctrl+Tab',
    PREV_TAB: 'Ctrl+Shift+Tab',
    CLOSE_TAB: 'Ctrl+W',
    GO_BACK: 'Alt+Left',
    GO_FORWARD: 'Alt+Right',
  },
}

interface UseKeyboardShortcutsOptions {
  enableGlobal?: boolean
  enableData?: boolean
  enableNavigation?: boolean
}

export const useKeyboardShortcuts = (options: UseKeyboardShortcutsOptions = {}) => {
  const {
    enableGlobal = true,
    enableData = true,
    enableNavigation = true,
  } = options

  const navigate = useNavigate()
  const { toggleTheme } = useThemeStore()
  const { connections, setActiveConnection } = useConnectionStore()

  // 全局快捷键
  if (enableGlobal) {
    // 搜索 - Ctrl+K
    useHotkeys('ctrl+k', (e) => {
      e.preventDefault()
      // 触发全局搜索
      const searchInput = document.querySelector('[data-search-input]') as HTMLInputElement
      if (searchInput) {
        searchInput.focus()
        searchInput.select()
      }
    }, { enableOnFormTags: false })

    // 刷新 - F5
    useHotkeys('f5', (e) => {
      e.preventDefault()
      // 触发当前页面刷新
      window.location.reload()
    })

    // 新建连接 - Ctrl+N
    useHotkeys('ctrl+n', (e) => {
      e.preventDefault()
      navigate('/connections/new')
    }, { enableOnFormTags: false })

    // 切换主题 - Ctrl+Shift+T
    useHotkeys('ctrl+shift+t', (e) => {
      e.preventDefault()
      toggleTheme()
      message.success('主题已切换')
    }, { enableOnFormTags: false })

    // 帮助 - F1
    useHotkeys('f1', (e) => {
      e.preventDefault()
      navigate('/help')
    })

    // 命令面板 - Ctrl+Shift+P
    useHotkeys('ctrl+shift+p', (e) => {
      e.preventDefault()
      // 打开命令面板
      message.info('命令面板功能即将推出')
    }, { enableOnFormTags: false })
  }

  // 数据操作快捷键
  if (enableData) {
    // 新建键 - Ctrl+Shift+N
    useHotkeys('ctrl+shift+n', (e) => {
      e.preventDefault()
      // 触发新建键对话框
      const event = new CustomEvent('redis:new-key')
      window.dispatchEvent(event)
    }, { enableOnFormTags: false })

    // 删除键 - Delete
    useHotkeys('delete', (e) => {
      // 只在选中键时生效
      const selectedKey = document.querySelector('[data-selected-key]')
      if (selectedKey) {
        e.preventDefault()
        const event = new CustomEvent('redis:delete-key')
        window.dispatchEvent(event)
      }
    }, { enableOnFormTags: false })

    // 编辑键 - F2
    useHotkeys('f2', (e) => {
      const selectedKey = document.querySelector('[data-selected-key]')
      if (selectedKey) {
        e.preventDefault()
        const event = new CustomEvent('redis:edit-key')
        window.dispatchEvent(event)
      }
    }, { enableOnFormTags: false })

    // 复制键 - Ctrl+C
    useHotkeys('ctrl+c', (e) => {
      const selectedKey = document.querySelector('[data-selected-key]')
      if (selectedKey && !window.getSelection()?.toString()) {
        e.preventDefault()
        const event = new CustomEvent('redis:copy-key')
        window.dispatchEvent(event)
      }
    }, { enableOnFormTags: false })

    // 撤销 - Ctrl+Z
    useHotkeys('ctrl+z', (e) => {
      // 只在编辑器中生效
      const editor = document.querySelector('[data-editor-active]')
      if (editor) {
        e.preventDefault()
        const event = new CustomEvent('redis:undo')
        window.dispatchEvent(event)
      }
    }, { enableOnFormTags: false })

    // 重做 - Ctrl+Y
    useHotkeys('ctrl+y', (e) => {
      const editor = document.querySelector('[data-editor-active]')
      if (editor) {
        e.preventDefault()
        const event = new CustomEvent('redis:redo')
        window.dispatchEvent(event)
      }
    }, { enableOnFormTags: false })
  }

  // 导航快捷键
  if (enableNavigation) {
    // 下一个标签 - Ctrl+Tab
    useHotkeys('ctrl+tab', (e) => {
      e.preventDefault()
      // 切换到下一个连接
      if (connections.length > 1) {
        const currentIndex = connections.findIndex(conn => conn.id === useConnectionStore.getState().activeConnectionId)
        const nextIndex = (currentIndex + 1) % connections.length
        setActiveConnection(connections[nextIndex].id)
      }
    }, { enableOnFormTags: false })

    // 上一个标签 - Ctrl+Shift+Tab
    useHotkeys('ctrl+shift+tab', (e) => {
      e.preventDefault()
      if (connections.length > 1) {
        const currentIndex = connections.findIndex(conn => conn.id === useConnectionStore.getState().activeConnectionId)
        const prevIndex = currentIndex === 0 ? connections.length - 1 : currentIndex - 1
        setActiveConnection(connections[prevIndex].id)
      }
    }, { enableOnFormTags: false })

    // 关闭标签 - Ctrl+W
    useHotkeys('ctrl+w', (e) => {
      e.preventDefault()
      const event = new CustomEvent('redis:close-connection')
      window.dispatchEvent(event)
    }, { enableOnFormTags: false })

    // 后退 - Alt+Left
    useHotkeys('alt+left', (e) => {
      e.preventDefault()
      navigate(-1)
    })

    // 前进 - Alt+Right
    useHotkeys('alt+right', (e) => {
      e.preventDefault()
      navigate(1)
    })
  }

  // 数字键快速切换连接 - Ctrl+1-9
  if (enableNavigation && connections.length > 0) {
    for (let i = 1; i <= Math.min(9, connections.length); i++) {
      useHotkeys(`ctrl+${i}`, (e) => {
        e.preventDefault()
        const connection = connections[i - 1]
        if (connection) {
          setActiveConnection(connection.id)
        }
      }, { enableOnFormTags: false })
    }
  }

  return {
    shortcuts: KEYBOARD_SHORTCUTS,
  }
}
