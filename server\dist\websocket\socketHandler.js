"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.setupWebSocket = setupWebSocket;
exports.getWebSocketManager = getWebSocketManager;
const logger_1 = require("../utils/logger");
const RedisConnectionManager_1 = require("../services/RedisConnectionManager");
class WebSocketManager {
    constructor(io) {
        this.clients = new Map();
        this.connectionMonitors = new Map();
        this.io = io;
    }
    setupEventHandlers() {
        this.io.on('connection', (socket) => {
            logger_1.logger.info(`WebSocket client connected: ${socket.id}`);
            this.clients.set(socket.id, {
                id: socket.id,
                connectedAt: new Date(),
            });
            socket.emit('connected', {
                clientId: socket.id,
                timestamp: new Date().toISOString(),
            });
            socket.on('subscribe-connection', (connectionId) => {
                this.handleConnectionSubscription(socket, connectionId);
            });
            socket.on('unsubscribe-connection', () => {
                this.handleConnectionUnsubscription(socket);
            });
            socket.on('request-realtime-data', (data) => {
                this.handleRealtimeDataRequest(socket, data);
            });
            socket.on('disconnect', () => {
                logger_1.logger.info(`WebSocket client disconnected: ${socket.id}`);
                this.handleClientDisconnect(socket.id);
            });
            socket.on('error', (error) => {
                logger_1.logger.error(`WebSocket error for client ${socket.id}:`, error);
            });
        });
    }
    handleConnectionSubscription(socket, connectionId) {
        const clientInfo = this.clients.get(socket.id);
        if (!clientInfo)
            return;
        this.handleConnectionUnsubscription(socket);
        clientInfo.connectionId = connectionId;
        this.clients.set(socket.id, clientInfo);
        socket.join(`connection:${connectionId}`);
        this.startConnectionMonitoring(connectionId);
        logger_1.logger.info(`Client ${socket.id} subscribed to connection ${connectionId}`);
        this.sendConnectionStatus(connectionId);
    }
    handleConnectionUnsubscription(socket) {
        const clientInfo = this.clients.get(socket.id);
        if (!clientInfo || !clientInfo.connectionId)
            return;
        const connectionId = clientInfo.connectionId;
        socket.leave(`connection:${connectionId}`);
        clientInfo.connectionId = undefined;
        this.clients.set(socket.id, clientInfo);
        const hasOtherSubscribers = Array.from(this.clients.values())
            .some(client => client.connectionId === connectionId && client.id !== socket.id);
        if (!hasOtherSubscribers) {
            this.stopConnectionMonitoring(connectionId);
        }
        logger_1.logger.info(`Client ${socket.id} unsubscribed from connection ${connectionId}`);
    }
    handleRealtimeDataRequest(socket, data) {
        const { connectionId, type } = data;
        const connectionInfo = RedisConnectionManager_1.connectionManager.getConnectionInfo(connectionId);
        if (!connectionInfo) {
            socket.emit('error', { message: 'Connection not found' });
            return;
        }
        switch (type) {
            case 'stats':
                this.sendConnectionStats(connectionId);
                break;
            case 'info':
                this.sendConnectionInfo(connectionId);
                break;
            default:
                socket.emit('error', { message: 'Unknown data type' });
        }
    }
    handleClientDisconnect(clientId) {
        const clientInfo = this.clients.get(clientId);
        if (!clientInfo)
            return;
        if (clientInfo.connectionId) {
            const connectionId = clientInfo.connectionId;
            const hasOtherSubscribers = Array.from(this.clients.values())
                .some(client => client.connectionId === connectionId && client.id !== clientId);
            if (!hasOtherSubscribers) {
                this.stopConnectionMonitoring(connectionId);
            }
        }
        this.clients.delete(clientId);
    }
    startConnectionMonitoring(connectionId) {
        this.stopConnectionMonitoring(connectionId);
        const interval = setInterval(() => {
            this.checkConnectionStatus(connectionId);
        }, 5000);
        this.connectionMonitors.set(connectionId, interval);
        logger_1.logger.info(`Started monitoring connection ${connectionId}`);
    }
    stopConnectionMonitoring(connectionId) {
        const interval = this.connectionMonitors.get(connectionId);
        if (interval) {
            clearInterval(interval);
            this.connectionMonitors.delete(connectionId);
            logger_1.logger.info(`Stopped monitoring connection ${connectionId}`);
        }
    }
    async checkConnectionStatus(connectionId) {
        try {
            const connectionInfo = RedisConnectionManager_1.connectionManager.getConnectionInfo(connectionId);
            if (!connectionInfo) {
                this.io.to(`connection:${connectionId}`).emit('connection-status', {
                    connectionId,
                    status: 'disconnected',
                    timestamp: new Date().toISOString(),
                });
                this.stopConnectionMonitoring(connectionId);
                return;
            }
            const { client } = connectionInfo;
            await client.ping();
            this.io.to(`connection:${connectionId}`).emit('connection-status', {
                connectionId,
                status: 'connected',
                timestamp: new Date().toISOString(),
            });
        }
        catch (error) {
            this.io.to(`connection:${connectionId}`).emit('connection-status', {
                connectionId,
                status: 'error',
                error: error instanceof Error ? error.message : 'Unknown error',
                timestamp: new Date().toISOString(),
            });
        }
    }
    async sendConnectionStatus(connectionId) {
        try {
            const connectionInfo = RedisConnectionManager_1.connectionManager.getConnectionInfo(connectionId);
            if (!connectionInfo) {
                this.io.to(`connection:${connectionId}`).emit('connection-status', {
                    connectionId,
                    status: 'disconnected',
                    timestamp: new Date().toISOString(),
                });
                return;
            }
            const { client } = connectionInfo;
            await client.ping();
            this.io.to(`connection:${connectionId}`).emit('connection-status', {
                connectionId,
                status: 'connected',
                timestamp: new Date().toISOString(),
            });
        }
        catch (error) {
            this.io.to(`connection:${connectionId}`).emit('connection-status', {
                connectionId,
                status: 'error',
                error: error instanceof Error ? error.message : 'Unknown error',
                timestamp: new Date().toISOString(),
            });
        }
    }
    async sendConnectionStats(connectionId) {
        try {
            const connectionInfo = RedisConnectionManager_1.connectionManager.getConnectionInfo(connectionId);
            if (!connectionInfo)
                return;
            const { client } = connectionInfo;
            const info = await client.info();
            const lines = info.split('\r\n');
            const stats = {};
            for (const line of lines) {
                if (line.includes(':')) {
                    const [key, value] = line.split(':');
                    stats[key] = value;
                }
            }
            this.io.to(`connection:${connectionId}`).emit('realtime-stats', {
                connectionId,
                stats: {
                    connected_clients: stats.connected_clients || '0',
                    used_memory_human: stats.used_memory_human || '0B',
                    instantaneous_ops_per_sec: stats.instantaneous_ops_per_sec || '0',
                    keyspace_hits: stats.keyspace_hits || '0',
                    keyspace_misses: stats.keyspace_misses || '0',
                },
                timestamp: new Date().toISOString(),
            });
        }
        catch (error) {
            logger_1.logger.error(`Failed to send connection stats for ${connectionId}:`, error);
        }
    }
    async sendConnectionInfo(connectionId) {
        try {
            const connectionInfo = RedisConnectionManager_1.connectionManager.getConnectionInfo(connectionId);
            if (!connectionInfo)
                return;
            const { config } = connectionInfo;
            this.io.to(`connection:${connectionId}`).emit('connection-info', {
                connectionId,
                info: {
                    name: config.name,
                    host: config.host,
                    port: config.port,
                    database: config.database || 0,
                },
                timestamp: new Date().toISOString(),
            });
        }
        catch (error) {
            logger_1.logger.error(`Failed to send connection info for ${connectionId}:`, error);
        }
    }
    broadcast(event, data) {
        this.io.emit(event, data);
    }
    sendToConnection(connectionId, event, data) {
        this.io.to(`connection:${connectionId}`).emit(event, data);
    }
    getConnectionSubscribers(connectionId) {
        return Array.from(this.clients.values())
            .filter(client => client.connectionId === connectionId).length;
    }
    getTotalClients() {
        return this.clients.size;
    }
}
let wsManager;
function setupWebSocket(io) {
    wsManager = new WebSocketManager(io);
    wsManager.setupEventHandlers();
    logger_1.logger.info('WebSocket handlers setup completed');
}
function getWebSocketManager() {
    return wsManager;
}
//# sourceMappingURL=socketHandler.js.map