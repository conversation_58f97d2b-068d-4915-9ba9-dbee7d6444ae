import React, { useState } from 'react';
import {
  Card,
  Button,
  Select,
  Input,
  Upload,
  Switch,
  Form,
  Row,
  Col,
  Typography,
  Space,
  message,
  Progress,
  Alert,
  Divider,
} from 'antd';
import {
  DownloadOutlined,
  UploadOutlined,
  FileTextOutlined,
  DatabaseOutlined,
} from '@ant-design/icons';
import { useConnectionStore } from '../stores/connectionStore';
import { useRedisStore } from '../stores/redisStore';
import { ApiService, ImportResult } from '../services/api';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const DataImportExport: React.FC = () => {
  const [exportLoading, setExportLoading] = useState(false);
  const [importLoading, setImportLoading] = useState(false);
  const [importResult, setImportResult] = useState<ImportResult | null>(null);
  const [form] = Form.useForm();
  
  const { activeConnectionId } = useConnectionStore();
  const { currentDatabase, databases } = useRedisStore();

  const handleExport = async (values: any) => {
    if (!activeConnectionId) {
      message.error('请先选择一个Redis连接');
      return;
    }

    setExportLoading(true);
    try {
      const blob = await ApiService.exportRedisData(activeConnectionId, {
        format: values.exportFormat,
        pattern: values.exportPattern || '*',
        db: values.exportDatabase,
      });

      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const extension = values.exportFormat === 'csv' ? 'csv' : 'json';
      link.download = `redis-export-${timestamp}.${extension}`;
      
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      message.success('数据导出成功！');
    } catch (error: any) {
      message.error(error.response?.data?.error || error.message || '导出失败');
    } finally {
      setExportLoading(false);
    }
  };

  const handleImport = async (values: any) => {
    if (!activeConnectionId) {
      message.error('请先选择一个Redis连接');
      return;
    }

    let importData;
    try {
      importData = JSON.parse(values.importData);
    } catch (error) {
      message.error('导入数据格式错误，请确保是有效的JSON格式');
      return;
    }

    setImportLoading(true);
    setImportResult(null);
    
    try {
      const response = await ApiService.importRedisData(activeConnectionId, importData, {
        db: values.importDatabase,
        overwrite: values.overwrite,
      });

      if (response.success && response.data) {
        setImportResult(response.data);
        message.success(response.message || '数据导入完成');
      } else {
        message.error(response.error || '导入失败');
      }
    } catch (error: any) {
      message.error(error.response?.data?.error || error.message || '导入失败');
    } finally {
      setImportLoading(false);
    }
  };

  const handleFileUpload = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string;
        form.setFieldsValue({ importData: content });
        message.success('文件读取成功');
      } catch (error) {
        message.error('文件读取失败');
      }
    };
    reader.readAsText(file);
    return false; // 阻止自动上传
  };

  if (!activeConnectionId) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <Title level={4} type="secondary">
            请先选择一个Redis连接
          </Title>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 h-full overflow-auto">
      <Title level={4} className="mb-4">
        <DatabaseOutlined className="mr-2" />
        数据导入导出
      </Title>

      <Row gutter={24}>
        {/* 数据导出 */}
        <Col span={12}>
          <Card title="数据导出" className="h-full">
            <Form
              layout="vertical"
              onFinish={handleExport}
              initialValues={{
                exportFormat: 'json',
                exportPattern: '*',
                exportDatabase: currentDatabase,
              }}
            >
              <Form.Item
                name="exportDatabase"
                label="数据库"
                rules={[{ required: true, message: '请选择数据库' }]}
              >
                <Select placeholder="选择数据库">
                  {databases.map((db) => (
                    <Option key={db.db} value={db.db}>
                      DB {db.db} ({db.keys} keys)
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                name="exportPattern"
                label="键模式"
                rules={[{ required: true, message: '请输入键模式' }]}
              >
                <Input placeholder="例如: user:* 或 * (所有键)" />
              </Form.Item>

              <Form.Item
                name="exportFormat"
                label="导出格式"
                rules={[{ required: true, message: '请选择导出格式' }]}
              >
                <Select>
                  <Option value="json">JSON格式</Option>
                  <Option value="csv">CSV格式</Option>
                </Select>
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  icon={<DownloadOutlined />}
                  loading={exportLoading}
                  block
                >
                  导出数据
                </Button>
              </Form.Item>
            </Form>

            <Alert
              message="导出说明"
              description="导出功能会将匹配的键值对保存为文件。JSON格式包含完整的元数据信息，CSV格式适合在表格软件中查看。"
              type="info"
              showIcon
              className="mt-4"
            />
          </Card>
        </Col>

        {/* 数据导入 */}
        <Col span={12}>
          <Card title="数据导入" className="h-full">
            <Form
              form={form}
              layout="vertical"
              onFinish={handleImport}
              initialValues={{
                importDatabase: currentDatabase,
                overwrite: false,
              }}
            >
              <Form.Item
                name="importDatabase"
                label="目标数据库"
                rules={[{ required: true, message: '请选择目标数据库' }]}
              >
                <Select placeholder="选择目标数据库">
                  {databases.map((db) => (
                    <Option key={db.db} value={db.db}>
                      DB {db.db} ({db.keys} keys)
                    </Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item name="overwrite" label="覆盖已存在的键" valuePropName="checked">
                <Switch />
              </Form.Item>

              <Form.Item
                name="importData"
                label="导入数据"
                rules={[{ required: true, message: '请输入或上传导入数据' }]}
              >
                <TextArea
                  rows={8}
                  placeholder="请粘贴JSON格式的导入数据，或使用下方按钮上传文件"
                  className="font-mono text-sm"
                />
              </Form.Item>

              <Form.Item>
                <Space>
                  <Upload
                    accept=".json,.txt"
                    beforeUpload={handleFileUpload}
                    showUploadList={false}
                  >
                    <Button icon={<FileTextOutlined />}>
                      选择文件
                    </Button>
                  </Upload>
                  <Button
                    type="primary"
                    htmlType="submit"
                    icon={<UploadOutlined />}
                    loading={importLoading}
                  >
                    导入数据
                  </Button>
                </Space>
              </Form.Item>
            </Form>

            {importResult && (
              <div className="mt-4">
                <Divider />
                <Title level={5}>导入结果</Title>
                <Row gutter={16}>
                  <Col span={6}>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">
                        {importResult.total}
                      </div>
                      <div className="text-gray-500">总计</div>
                    </div>
                  </Col>
                  <Col span={6}>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">
                        {importResult.success}
                      </div>
                      <div className="text-gray-500">成功</div>
                    </div>
                  </Col>
                  <Col span={6}>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-red-600">
                        {importResult.failed}
                      </div>
                      <div className="text-gray-500">失败</div>
                    </div>
                  </Col>
                  <Col span={6}>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-orange-600">
                        {importResult.skipped}
                      </div>
                      <div className="text-gray-500">跳过</div>
                    </div>
                  </Col>
                </Row>

                <Progress
                  percent={Math.round((importResult.success / importResult.total) * 100)}
                  status={importResult.failed > 0 ? 'exception' : 'success'}
                  className="mt-4"
                />

                {importResult.errors.length > 0 && (
                  <Alert
                    message="导入错误"
                    description={
                      <div className="max-h-32 overflow-auto">
                        {importResult.errors.map((error, index) => (
                          <div key={index} className="text-sm">
                            {error}
                          </div>
                        ))}
                      </div>
                    }
                    type="error"
                    showIcon
                    className="mt-4"
                  />
                )}
              </div>
            )}

            <Alert
              message="导入说明"
              description="导入功能支持JSON格式的数据。请确保数据格式正确，包含key、type、value、ttl等字段。建议先导出数据查看格式示例。"
              type="info"
              showIcon
              className="mt-4"
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default DataImportExport;
