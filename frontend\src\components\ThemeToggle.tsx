import React from 'react';
import { But<PERSON>, Dropdown, Space, Typography, Divider, Tooltip } from 'antd';
import type { MenuProps } from 'antd';
import {
  SunOutlined,
  MoonOutlined,
  DesktopOutlined,
  BgColorsOutlined,
  CheckOutlined,
} from '@ant-design/icons';
import { useTheme, THEME_COLORS, ThemeMode } from '../stores/themeStore';

const { Text } = Typography;

interface ThemeToggleProps {
  showText?: boolean;
  size?: 'small' | 'middle' | 'large';
}

const ThemeToggle: React.FC<ThemeToggleProps> = ({ 
  showText = false, 
  size = 'middle' 
}) => {
  const { mode, currentTheme, primaryColor, setMode, setPrimaryColor, toggleTheme } = useTheme();

  // 获取当前主题图标
  const getCurrentThemeIcon = () => {
    switch (mode) {
      case 'light':
        return <SunOutlined />;
      case 'dark':
        return <MoonOutlined />;
      case 'auto':
        return <DesktopOutlined />;
      default:
        return <SunOutlined />;
    }
  };

  // 获取主题模式菜单项
  const getThemeModeItems = (): MenuProps['items'] => [
    {
      key: 'light',
      icon: <SunOutlined />,
      label: (
        <Space>
          <span>浅色主题</span>
          {mode === 'light' && <CheckOutlined className="text-blue-500" />}
        </Space>
      ),
      onClick: () => setMode('light'),
    },
    {
      key: 'dark',
      icon: <MoonOutlined />,
      label: (
        <Space>
          <span>深色主题</span>
          {mode === 'dark' && <CheckOutlined className="text-blue-500" />}
        </Space>
      ),
      onClick: () => setMode('dark'),
    },
    {
      key: 'auto',
      icon: <DesktopOutlined />,
      label: (
        <Space>
          <span>跟随系统</span>
          {mode === 'auto' && <CheckOutlined className="text-blue-500" />}
        </Space>
      ),
      onClick: () => setMode('auto'),
    },
    {
      type: 'divider',
    },
    {
      key: 'colors',
      icon: <BgColorsOutlined />,
      label: '主题色',
      children: THEME_COLORS.map((color) => ({
        key: color.value,
        label: (
          <Space>
            <div
              className="w-4 h-4 rounded-full border border-gray-300"
              style={{ backgroundColor: color.value }}
            />
            <span>{color.name}</span>
            {primaryColor === color.value && <CheckOutlined className="text-blue-500" />}
          </Space>
        ),
        onClick: () => setPrimaryColor(color.value),
      })),
    },
  ];

  // 简单模式：只有切换按钮
  if (!showText) {
    return (
      <Tooltip title={`当前: ${mode === 'light' ? '浅色' : mode === 'dark' ? '深色' : '跟随系统'}`}>
        <Button
          type="text"
          size={size}
          icon={getCurrentThemeIcon()}
          onClick={toggleTheme}
        />
      </Tooltip>
    );
  }

  // 完整模式：下拉菜单
  return (
    <Dropdown
      menu={{ items: getThemeModeItems() }}
      trigger={['click']}
      placement="bottomRight"
    >
      <Button type="text" size={size}>
        <Space>
          {getCurrentThemeIcon()}
          <span>主题</span>
        </Space>
      </Button>
    </Dropdown>
  );
};

export default ThemeToggle;
