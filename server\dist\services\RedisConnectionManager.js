"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.connectionManager = exports.RedisConnectionManager = exports.ConnectionStatus = void 0;
const ioredis_1 = __importDefault(require("ioredis"));
const events_1 = require("events");
var ConnectionStatus;
(function (ConnectionStatus) {
    ConnectionStatus["DISCONNECTED"] = "disconnected";
    ConnectionStatus["CONNECTING"] = "connecting";
    ConnectionStatus["CONNECTED"] = "connected";
    ConnectionStatus["ERROR"] = "error";
    ConnectionStatus["RECONNECTING"] = "reconnecting";
})(ConnectionStatus || (exports.ConnectionStatus = ConnectionStatus = {}));
class RedisConnectionManager extends events_1.EventEmitter {
    constructor() {
        super();
        this.connections = new Map();
        this.connectionTimeouts = new Map();
        this.setMaxListeners(100);
    }
    async createConnection(config) {
        try {
            if (this.connections.has(config.id)) {
                await this.removeConnection(config.id);
            }
            const redisOptions = {
                host: config.host,
                port: config.port,
                password: config.password,
                db: config.database,
                connectTimeout: config.timeout,
                lazyConnect: config.lazyConnect,
                maxRetriesPerRequest: config.maxRetriesPerRequest,
                enableReadyCheck: true,
            };
            if (config.ssl) {
                redisOptions.tls = {};
            }
            const client = new ioredis_1.default(redisOptions);
            const connectionInfo = {
                id: config.id,
                config,
                client,
                status: ConnectionStatus.CONNECTING,
            };
            this.connections.set(config.id, connectionInfo);
            this.setupClientEventHandlers(connectionInfo);
            if (!config.lazyConnect) {
                await client.connect();
            }
            this.emit('connectionCreated', config.id);
            return config.id;
        }
        catch (error) {
            this.emit('connectionError', config.id, error);
            throw error;
        }
    }
    setupClientEventHandlers(connectionInfo) {
        const { id, client } = connectionInfo;
        client.on('connect', () => {
            this.updateConnectionStatus(id, ConnectionStatus.CONNECTING);
            this.emit('connectionStatusChanged', id, ConnectionStatus.CONNECTING);
        });
        client.on('ready', () => {
            const info = this.connections.get(id);
            if (info) {
                info.connectedAt = new Date();
                info.lastActivity = new Date();
                this.updateConnectionStatus(id, ConnectionStatus.CONNECTED);
                this.emit('connectionStatusChanged', id, ConnectionStatus.CONNECTED);
            }
        });
        client.on('error', (error) => {
            const info = this.connections.get(id);
            if (info) {
                info.lastError = error.message;
                this.updateConnectionStatus(id, ConnectionStatus.ERROR);
                this.emit('connectionError', id, error);
                this.emit('connectionStatusChanged', id, ConnectionStatus.ERROR);
            }
        });
        client.on('close', () => {
            this.updateConnectionStatus(id, ConnectionStatus.DISCONNECTED);
            this.emit('connectionStatusChanged', id, ConnectionStatus.DISCONNECTED);
        });
        client.on('reconnecting', () => {
            this.updateConnectionStatus(id, ConnectionStatus.RECONNECTING);
            this.emit('connectionStatusChanged', id, ConnectionStatus.RECONNECTING);
        });
        client.on('end', () => {
            this.updateConnectionStatus(id, ConnectionStatus.DISCONNECTED);
            this.emit('connectionStatusChanged', id, ConnectionStatus.DISCONNECTED);
        });
    }
    updateConnectionStatus(id, status) {
        const connectionInfo = this.connections.get(id);
        if (connectionInfo) {
            connectionInfo.status = status;
            connectionInfo.lastActivity = new Date();
        }
    }
    getConnection(id) {
        const connectionInfo = this.connections.get(id);
        return connectionInfo?.client || null;
    }
    getConnectionInfo(id) {
        return this.connections.get(id) || null;
    }
    getAllConnections() {
        return Array.from(this.connections.values());
    }
    getActiveConnections() {
        return this.getAllConnections().filter(conn => conn.status === ConnectionStatus.CONNECTED);
    }
    async testConnection(config) {
        const testClient = new ioredis_1.default({
            host: config.host,
            port: config.port,
            password: config.password,
            db: config.database,
            connectTimeout: config.timeout,
            lazyConnect: true,
            maxRetriesPerRequest: 1,
            ...(config.ssl && { tls: {} }),
        });
        try {
            await testClient.connect();
            await testClient.ping();
            await testClient.disconnect();
            return true;
        }
        catch (error) {
            try {
                await testClient.disconnect();
            }
            catch (disconnectError) {
            }
            return false;
        }
    }
    async removeConnection(id) {
        const connectionInfo = this.connections.get(id);
        if (!connectionInfo) {
            return;
        }
        try {
            const timeout = this.connectionTimeouts.get(id);
            if (timeout) {
                clearTimeout(timeout);
                this.connectionTimeouts.delete(id);
            }
            if (connectionInfo.client.status !== 'end') {
                await connectionInfo.client.disconnect();
            }
            this.connections.delete(id);
            this.emit('connectionRemoved', id);
        }
        catch (error) {
            console.error(`Error removing connection ${id}:`, error);
            this.connections.delete(id);
            this.emit('connectionRemoved', id);
        }
    }
    async closeAllConnections() {
        const connectionIds = Array.from(this.connections.keys());
        await Promise.all(connectionIds.map(id => this.removeConnection(id)));
    }
    getConnectionStats() {
        const connections = this.getAllConnections();
        const stats = {
            total: connections.length,
            connected: 0,
            connecting: 0,
            disconnected: 0,
            error: 0,
            reconnecting: 0,
        };
        connections.forEach(conn => {
            switch (conn.status) {
                case ConnectionStatus.CONNECTED:
                    stats.connected++;
                    break;
                case ConnectionStatus.CONNECTING:
                    stats.connecting++;
                    break;
                case ConnectionStatus.DISCONNECTED:
                    stats.disconnected++;
                    break;
                case ConnectionStatus.ERROR:
                    stats.error++;
                    break;
                case ConnectionStatus.RECONNECTING:
                    stats.reconnecting++;
                    break;
            }
        });
        return stats;
    }
}
exports.RedisConnectionManager = RedisConnectionManager;
exports.connectionManager = new RedisConnectionManager();
//# sourceMappingURL=RedisConnectionManager.js.map