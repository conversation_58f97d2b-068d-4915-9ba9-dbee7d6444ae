{"name": "redis-client-server", "version": "1.0.0", "description": "Redis proxy server for Redis visual client", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "type-check": "tsc --noEmit"}, "dependencies": {"cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "ioredis": "^5.6.1", "socket.io": "^4.8.1"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/node": "^20.10.5", "@types/socket.io": "^3.0.1", "tsx": "^4.6.2", "typescript": "^5.3.3"}, "keywords": ["redis", "proxy", "api", "express"], "author": "Redis Client Team", "license": "MIT"}