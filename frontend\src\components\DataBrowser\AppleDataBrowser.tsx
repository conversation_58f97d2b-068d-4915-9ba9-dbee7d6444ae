import React, { useEffect, useState } from 'react';
import {
  Input,
  Select,
  Typography,
  Button,
  Tag,
  Spin,
  Empty,
  message,
  Tooltip,
} from 'antd';
import {
  SearchOutlined,
  ReloadOutlined,
  DatabaseOutlined,
  KeyOutlined,
  EyeOutlined,
} from '@ant-design/icons';
import { useRedisStore } from '../../stores/redisStore';
import { useConnectionStore } from '../../stores/connectionStore';
import KeyValueViewer from '../KeyValueViewer';

const { Search } = Input;
const { Option } = Select;
const { Text, Title } = Typography;

const AppleDataBrowser: React.FC = () => {
  const [searchValue, setSearchValue] = useState('*');
  const [selectedKeyType, setSelectedKeyType] = useState<string>('all');
  const { activeConnectionId } = useConnectionStore();
  
  const {
    databases,
    currentDatabase,
    keys,
    selectedKey,
    hasMoreKeys,
    loading,
    error,
    fetchDatabases,
    setCurrentDatabase,
    fetchKeys,
    loadMoreKeys,
    setSearchPattern,
    selectKey,
    clearError,
  } = useRedisStore();

  useEffect(() => {
    if (activeConnectionId) {
      fetchDatabases(activeConnectionId);
      fetchKeys(activeConnectionId);
    }
  }, [activeConnectionId, fetchDatabases, fetchKeys]);

  useEffect(() => {
    if (error) {
      message.error(error);
      clearError();
    }
  }, [error, clearError]);

  const handleDatabaseChange = (db: number) => {
    setCurrentDatabase(db);
    if (activeConnectionId) {
      fetchKeys(activeConnectionId);
    }
  };

  const handleSearch = (value: string) => {
    setSearchValue(value);
    setSearchPattern(value || '*');
    if (activeConnectionId) {
      fetchKeys(activeConnectionId);
    }
  };

  const handleRefresh = () => {
    if (activeConnectionId) {
      fetchKeys(activeConnectionId);
    }
  };

  const handleKeySelect = (keyName: string) => {
    selectKey(keyName);
  };

  const handleLoadMore = () => {
    if (activeConnectionId && hasMoreKeys) {
      loadMoreKeys(activeConnectionId);
    }
  };

  const getTypeColor = (type: string) => {
    const colors: Record<string, string> = {
      string: 'var(--apple-blue-500)',
      hash: 'var(--apple-success)',
      list: 'var(--apple-warning)',
      set: 'var(--apple-error)',
      zset: 'purple',
      stream: 'cyan',
    };
    return colors[type] || 'var(--text-secondary)';
  };

  const getKeyTypeOptions = () => {
    const types = ['all', ...new Set(keys.map(key => key.type))];
    return types.map(type => ({
      label: type === 'all' ? '全部类型' : type.toUpperCase(),
      value: type,
    }));
  };

  const filteredKeys = selectedKeyType === 'all' 
    ? keys 
    : keys.filter(key => key.type === selectedKeyType);

  return (
    <div className="h-full flex">
      {/* 左侧键列表 */}
      <div className="w-96 border-r border-[var(--border-primary)] bg-[var(--bg-secondary)] flex flex-col">
        {/* 头部控制区 */}
        <div className="p-6 border-b border-[var(--border-primary)] bg-[var(--bg-primary)]">
          <div className="flex items-center justify-between mb-4">
            <Title level={4} className="mb-0 text-[var(--text-primary)]">
              <DatabaseOutlined className="mr-2" />
              数据浏览器
            </Title>
            <Tooltip title="刷新">
              <Button
                type="text"
                icon={<ReloadOutlined />}
                onClick={handleRefresh}
                loading={loading}
                className="text-[var(--text-secondary)] hover:text-[var(--text-primary)]"
              />
            </Tooltip>
          </div>
          
          {/* 数据库选择器 */}
          <div className="mb-4">
            <Text className="text-[var(--text-secondary)] text-sm mb-2 block">
              数据库
            </Text>
            <Select
              value={currentDatabase}
              onChange={handleDatabaseChange}
              className="w-full"
              placeholder="选择数据库"
            >
              {databases.map((db) => (
                <Option key={db.db} value={db.db}>
                  <div className="flex items-center justify-between">
                    <span>DB {db.db}</span>
                    <Tag color="blue">
                      {db.keys} keys
                    </Tag>
                  </div>
                </Option>
              ))}
            </Select>
          </div>

          {/* 搜索框 */}
          <div className="mb-4">
            <Text className="text-[var(--text-secondary)] text-sm mb-2 block">
              搜索键名
            </Text>
            <Search
              placeholder="支持通配符，如 user:*"
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              onSearch={handleSearch}
              enterButton={<SearchOutlined />}
              className="apple-input"
            />
          </div>

          {/* 类型筛选 */}
          <div>
            <Text className="text-[var(--text-secondary)] text-sm mb-2 block">
              数据类型
            </Text>
            <Select
              value={selectedKeyType}
              onChange={setSelectedKeyType}
              className="w-full"
              options={getKeyTypeOptions()}
            />
          </div>
        </div>

        {/* 键列表 */}
        <div className="flex-1 overflow-auto">
          <Spin spinning={loading}>
            {filteredKeys.length === 0 ? (
              <div className="p-6">
                <Empty
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                  description={
                    <Text className="text-[var(--text-secondary)]">
                      {searchValue === '*' ? '暂无数据' : '未找到匹配的键'}
                    </Text>
                  }
                />
              </div>
            ) : (
              <div className="p-2">
                {filteredKeys.map((keyInfo) => (
                  <div
                    key={keyInfo.key}
                    onClick={() => handleKeySelect(keyInfo.key)}
                    className={`
                      p-3 mb-2 rounded-lg cursor-pointer transition-all duration-200
                      ${selectedKey === keyInfo.key
                        ? 'bg-[var(--apple-blue-500)] text-white shadow-lg'
                        : 'bg-[var(--bg-primary)] hover:bg-[var(--bg-tertiary)] text-[var(--text-primary)]'
                      }
                    `}
                  >
                    <div className="flex items-center space-x-3">
                      <div
                        className={`
                          w-8 h-8 rounded-lg flex items-center justify-center text-sm
                          ${selectedKey === keyInfo.key
                            ? 'bg-white bg-opacity-20'
                            : 'bg-[var(--bg-secondary)]'
                          }
                        `}
                      >
                        <KeyOutlined />
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-1">
                          <Text
                            ellipsis={{ tooltip: keyInfo.key }}
                            className={`
                              font-medium
                              ${selectedKey === keyInfo.key
                                ? 'text-white'
                                : 'text-[var(--text-primary)]'
                              }
                            `}
                          >
                            {keyInfo.key}
                          </Text>
                          <Tag
                            color={selectedKey === keyInfo.key ? 'white' : undefined}
                            style={{
                              color: selectedKey === keyInfo.key 
                                ? 'var(--apple-blue-500)' 
                                : getTypeColor(keyInfo.type),
                              backgroundColor: selectedKey === keyInfo.key 
                                ? 'white' 
                                : `${getTypeColor(keyInfo.type)}15`,
                              border: 'none',
                            }}
                            className="text-xs font-medium"
                          >
                            {keyInfo.type.toUpperCase()}
                          </Tag>
                        </div>
                        
                        {keyInfo.ttl && keyInfo.ttl > 0 && (
                          <Text
                            className={`
                              text-xs
                              ${selectedKey === keyInfo.key
                                ? 'text-white text-opacity-75'
                                : 'text-[var(--text-tertiary)]'
                              }
                            `}
                          >
                            TTL: {keyInfo.ttl}s
                          </Text>
                        )}
                      </div>
                    </div>
                  </div>
                ))}

                {/* 加载更多按钮 */}
                {hasMoreKeys && (
                  <div className="p-3 text-center">
                    <Button
                      type="text"
                      onClick={handleLoadMore}
                      loading={loading}
                      className="text-[var(--apple-blue-500)] hover:bg-[var(--apple-blue-500)]15"
                    >
                      加载更多
                    </Button>
                  </div>
                )}
              </div>
            )}
          </Spin>
        </div>

        {/* 底部统计信息 */}
        <div className="p-4 border-t border-[var(--border-primary)] bg-[var(--bg-primary)]">
          <div className="flex items-center justify-between text-sm">
            <Text className="text-[var(--text-secondary)]">
              共 {filteredKeys.length} 个键
            </Text>
            {selectedKeyType !== 'all' && (
              <Tag color="blue">
                {selectedKeyType.toUpperCase()}
              </Tag>
            )}
          </div>
        </div>
      </div>

      {/* 右侧键值查看器 */}
      <div className="flex-1 bg-[var(--bg-primary)]">
        {selectedKey && activeConnectionId ? (
          <KeyValueViewer
            connectionId={activeConnectionId}
            keyName={selectedKey}
            database={currentDatabase}
          />
        ) : (
          <div className="h-full flex items-center justify-center">
            <div className="text-center max-w-md">
              <div className="w-16 h-16 bg-[var(--bg-secondary)] rounded-2xl flex items-center justify-center mx-auto mb-4">
                <EyeOutlined className="text-2xl text-[var(--text-tertiary)]" />
              </div>
              <Title level={4} className="text-[var(--text-secondary)] mb-2">
                选择一个键来查看
              </Title>
              <Text className="text-[var(--text-tertiary)]">
                从左侧列表中选择一个Redis键来查看其详细信息和值
              </Text>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AppleDataBrowser;
