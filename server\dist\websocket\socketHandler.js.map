{"version": 3, "file": "socketHandler.js", "sourceRoot": "", "sources": ["../../src/websocket/socketHandler.ts"], "names": [], "mappings": ";;AAyUA,wCAIC;AAED,kDAEC;AAhVD,4CAAyC;AACzC,+EAAuE;AAQvE,MAAM,gBAAgB;IAKpB,YAAY,EAAU;QAHd,YAAO,GAA4B,IAAI,GAAG,EAAE,CAAC;QAC7C,uBAAkB,GAAgC,IAAI,GAAG,EAAE,CAAC;QAGlE,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;IACf,CAAC;IAEM,kBAAkB;QACvB,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,YAAY,EAAE,CAAC,MAAc,EAAE,EAAE;YAC1C,eAAM,CAAC,IAAI,CAAC,+BAA+B,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;YAGxD,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE;gBAC1B,EAAE,EAAE,MAAM,CAAC,EAAE;gBACb,WAAW,EAAE,IAAI,IAAI,EAAE;aACxB,CAAC,CAAC;YAGH,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE;gBACvB,QAAQ,EAAE,MAAM,CAAC,EAAE;gBACnB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC,CAAC;YAGH,MAAM,CAAC,EAAE,CAAC,sBAAsB,EAAE,CAAC,YAAoB,EAAE,EAAE;gBACzD,IAAI,CAAC,4BAA4B,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;YAC1D,CAAC,CAAC,CAAC;YAGH,MAAM,CAAC,EAAE,CAAC,wBAAwB,EAAE,GAAG,EAAE;gBACvC,IAAI,CAAC,8BAA8B,CAAC,MAAM,CAAC,CAAC;YAC9C,CAAC,CAAC,CAAC;YAGH,MAAM,CAAC,EAAE,CAAC,uBAAuB,EAAE,CAAC,IAA4C,EAAE,EAAE;gBAClF,IAAI,CAAC,yBAAyB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YAC/C,CAAC,CAAC,CAAC;YAGH,MAAM,CAAC,EAAE,CAAC,YAAY,EAAE,GAAG,EAAE;gBAC3B,eAAM,CAAC,IAAI,CAAC,kCAAkC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;gBAC3D,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YACzC,CAAC,CAAC,CAAC;YAGH,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;gBAC3B,eAAM,CAAC,KAAK,CAAC,8BAA8B,MAAM,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YAClE,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,4BAA4B,CAAC,MAAc,EAAE,YAAoB;QACvE,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QAC/C,IAAI,CAAC,UAAU;YAAE,OAAO;QAGxB,IAAI,CAAC,8BAA8B,CAAC,MAAM,CAAC,CAAC;QAG5C,UAAU,CAAC,YAAY,GAAG,YAAY,CAAC;QACvC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;QAGxC,MAAM,CAAC,IAAI,CAAC,cAAc,YAAY,EAAE,CAAC,CAAC;QAG1C,IAAI,CAAC,yBAAyB,CAAC,YAAY,CAAC,CAAC;QAE7C,eAAM,CAAC,IAAI,CAAC,UAAU,MAAM,CAAC,EAAE,6BAA6B,YAAY,EAAE,CAAC,CAAC;QAG5E,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;IAC1C,CAAC;IAEO,8BAA8B,CAAC,MAAc;QACnD,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QAC/C,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,YAAY;YAAE,OAAO;QAEpD,MAAM,YAAY,GAAG,UAAU,CAAC,YAAY,CAAC;QAG7C,MAAM,CAAC,KAAK,CAAC,cAAc,YAAY,EAAE,CAAC,CAAC;QAG3C,UAAU,CAAC,YAAY,GAAG,SAAS,CAAC;QACpC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,UAAU,CAAC,CAAC;QAGxC,MAAM,mBAAmB,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;aAC1D,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,YAAY,KAAK,YAAY,IAAI,MAAM,CAAC,EAAE,KAAK,MAAM,CAAC,EAAE,CAAC,CAAC;QAGnF,IAAI,CAAC,mBAAmB,EAAE,CAAC;YACzB,IAAI,CAAC,wBAAwB,CAAC,YAAY,CAAC,CAAC;QAC9C,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,UAAU,MAAM,CAAC,EAAE,iCAAiC,YAAY,EAAE,CAAC,CAAC;IAClF,CAAC;IAEO,yBAAyB,CAAC,MAAc,EAAE,IAA4C;QAC5F,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC;QAGpC,MAAM,cAAc,GAAG,0CAAiB,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;QACzE,IAAI,CAAC,cAAc,EAAE,CAAC;YACpB,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC,CAAC;YAC1D,OAAO;QACT,CAAC;QAGD,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,OAAO;gBACV,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC;gBACvC,MAAM;YACR,KAAK,MAAM;gBACT,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC;gBACtC,MAAM;YACR;gBACE,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,mBAAmB,EAAE,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;IAEO,sBAAsB,CAAC,QAAgB;QAC7C,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC9C,IAAI,CAAC,UAAU;YAAE,OAAO;QAGxB,IAAI,UAAU,CAAC,YAAY,EAAE,CAAC;YAC5B,MAAM,YAAY,GAAG,UAAU,CAAC,YAAY,CAAC;YAG7C,MAAM,mBAAmB,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;iBAC1D,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,YAAY,KAAK,YAAY,IAAI,MAAM,CAAC,EAAE,KAAK,QAAQ,CAAC,CAAC;YAGlF,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBACzB,IAAI,CAAC,wBAAwB,CAAC,YAAY,CAAC,CAAC;YAC9C,CAAC;QACH,CAAC;QAGD,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IAChC,CAAC;IAEO,yBAAyB,CAAC,YAAoB;QAEpD,IAAI,CAAC,wBAAwB,CAAC,YAAY,CAAC,CAAC;QAG5C,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,EAAE;YAChC,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,CAAC;QAC3C,CAAC,EAAE,IAAI,CAAC,CAAC;QAET,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;QACpD,eAAM,CAAC,IAAI,CAAC,iCAAiC,YAAY,EAAE,CAAC,CAAC;IAC/D,CAAC;IAEO,wBAAwB,CAAC,YAAoB;QACnD,MAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QAC3D,IAAI,QAAQ,EAAE,CAAC;YACb,aAAa,CAAC,QAAQ,CAAC,CAAC;YACxB,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YAC7C,eAAM,CAAC,IAAI,CAAC,iCAAiC,YAAY,EAAE,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,YAAoB;QACtD,IAAI,CAAC;YACH,MAAM,cAAc,GAAG,0CAAiB,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;YACzE,IAAI,CAAC,cAAc,EAAE,CAAC;gBAEpB,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,cAAc,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,mBAAmB,EAAE;oBACjE,YAAY;oBACZ,MAAM,EAAE,cAAc;oBACtB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACpC,CAAC,CAAC;gBACH,IAAI,CAAC,wBAAwB,CAAC,YAAY,CAAC,CAAC;gBAC5C,OAAO;YACT,CAAC;YAGD,MAAM,EAAE,MAAM,EAAE,GAAG,cAAc,CAAC;YAClC,MAAO,MAAc,CAAC,IAAI,EAAE,CAAC;YAG7B,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,cAAc,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,mBAAmB,EAAE;gBACjE,YAAY;gBACZ,MAAM,EAAE,WAAW;gBACnB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAEf,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,cAAc,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,mBAAmB,EAAE;gBACjE,YAAY;gBACZ,MAAM,EAAE,OAAO;gBACf,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;gBAC/D,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAAC,YAAoB;QACrD,IAAI,CAAC;YACH,MAAM,cAAc,GAAG,0CAAiB,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;YACzE,IAAI,CAAC,cAAc,EAAE,CAAC;gBACpB,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,cAAc,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,mBAAmB,EAAE;oBACjE,YAAY;oBACZ,MAAM,EAAE,cAAc;oBACtB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACpC,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAGD,MAAM,EAAE,MAAM,EAAE,GAAG,cAAc,CAAC;YAClC,MAAO,MAAc,CAAC,IAAI,EAAE,CAAC;YAE7B,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,cAAc,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,mBAAmB,EAAE;gBACjE,YAAY;gBACZ,MAAM,EAAE,WAAW;gBACnB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,cAAc,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,mBAAmB,EAAE;gBACjE,YAAY;gBACZ,MAAM,EAAE,OAAO;gBACf,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;gBAC/D,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,YAAoB;QACpD,IAAI,CAAC;YACH,MAAM,cAAc,GAAG,0CAAiB,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;YACzE,IAAI,CAAC,cAAc;gBAAE,OAAO;YAE5B,MAAM,EAAE,MAAM,EAAE,GAAG,cAAc,CAAC;YAClC,MAAM,IAAI,GAAG,MAAO,MAAc,CAAC,IAAI,EAAE,CAAC;YAG1C,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YACjC,MAAM,KAAK,GAAQ,EAAE,CAAC;YAEtB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACzB,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;oBACvB,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;oBACrC,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;gBACrB,CAAC;YACH,CAAC;YAED,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,cAAc,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,gBAAgB,EAAE;gBAC9D,YAAY;gBACZ,KAAK,EAAE;oBACL,iBAAiB,EAAE,KAAK,CAAC,iBAAiB,IAAI,GAAG;oBACjD,iBAAiB,EAAE,KAAK,CAAC,iBAAiB,IAAI,IAAI;oBAClD,yBAAyB,EAAE,KAAK,CAAC,yBAAyB,IAAI,GAAG;oBACjE,aAAa,EAAE,KAAK,CAAC,aAAa,IAAI,GAAG;oBACzC,eAAe,EAAE,KAAK,CAAC,eAAe,IAAI,GAAG;iBAC9C;gBACD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,uCAAuC,YAAY,GAAG,EAAE,KAAK,CAAC,CAAC;QAC9E,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,YAAoB;QACnD,IAAI,CAAC;YACH,MAAM,cAAc,GAAG,0CAAiB,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;YACzE,IAAI,CAAC,cAAc;gBAAE,OAAO;YAE5B,MAAM,EAAE,MAAM,EAAE,GAAG,cAAc,CAAC;YAElC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,cAAc,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,iBAAiB,EAAE;gBAC/D,YAAY;gBACZ,IAAI,EAAE;oBACJ,IAAI,EAAE,MAAM,CAAC,IAAI;oBACjB,IAAI,EAAE,MAAM,CAAC,IAAI;oBACjB,IAAI,EAAE,MAAM,CAAC,IAAI;oBACjB,QAAQ,EAAE,MAAM,CAAC,QAAQ,IAAI,CAAC;iBAC/B;gBACD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,sCAAsC,YAAY,GAAG,EAAE,KAAK,CAAC,CAAC;QAC7E,CAAC;IACH,CAAC;IAGM,SAAS,CAAC,KAAa,EAAE,IAAS;QACvC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IAC5B,CAAC;IAGM,gBAAgB,CAAC,YAAoB,EAAE,KAAa,EAAE,IAAS;QACpE,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,cAAc,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IAC7D,CAAC;IAGM,wBAAwB,CAAC,YAAoB;QAClD,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;aACrC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,YAAY,KAAK,YAAY,CAAC,CAAC,MAAM,CAAC;IACnE,CAAC;IAGM,eAAe;QACpB,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;IAC3B,CAAC;CACF;AAED,IAAI,SAA2B,CAAC;AAEhC,SAAgB,cAAc,CAAC,EAAU;IACvC,SAAS,GAAG,IAAI,gBAAgB,CAAC,EAAE,CAAC,CAAC;IACrC,SAAS,CAAC,kBAAkB,EAAE,CAAC;IAC/B,eAAM,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;AACpD,CAAC;AAED,SAAgB,mBAAmB;IACjC,OAAO,SAAS,CAAC;AACnB,CAAC"}