/* 主题样式 */

/* 基础主题变量 */
:root {
  /* 浅色主题 */
  --theme-bg-primary: #ffffff;
  --theme-bg-secondary: #f5f5f5;
  --theme-bg-elevated: #ffffff;
  --theme-text-primary: rgba(0, 0, 0, 0.88);
  --theme-text-secondary: rgba(0, 0, 0, 0.65);
  --theme-text-tertiary: rgba(0, 0, 0, 0.45);
  --theme-border-color: rgba(0, 0, 0, 0.15);
  --theme-border-secondary: rgba(0, 0, 0, 0.06);
  
  /* 状态颜色 */
  --theme-success: #52c41a;
  --theme-warning: #faad14;
  --theme-error: #ff4d4f;
  --theme-info: #1890ff;
  
  /* 阴影 */
  --theme-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
  --theme-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --theme-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 深色主题 */
:root.dark {
  --theme-bg-primary: #141414;
  --theme-bg-secondary: #1f1f1f;
  --theme-bg-elevated: #262626;
  --theme-text-primary: #ffffff;
  --theme-text-secondary: rgba(255, 255, 255, 0.65);
  --theme-text-tertiary: rgba(255, 255, 255, 0.45);
  --theme-border-color: rgba(255, 255, 255, 0.15);
  --theme-border-secondary: rgba(255, 255, 255, 0.06);
  
  /* 状态颜色 - 深色主题下稍微调整 */
  --theme-success: #73d13d;
  --theme-warning: #ffc53d;
  --theme-error: #ff7875;
  --theme-info: #40a9ff;
  
  /* 阴影 - 深色主题下使用更深的阴影 */
  --theme-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.2), 0 1px 6px -1px rgba(0, 0, 0, 0.15), 0 2px 4px 0 rgba(0, 0, 0, 0.1);
  --theme-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --theme-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
}

/* 主题过渡动画 */
* {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

/* 自定义组件样式 */
.theme-card {
  background-color: var(--theme-bg-elevated);
  border: 1px solid var(--theme-border-color);
  box-shadow: var(--theme-shadow-sm);
}

.theme-text-primary {
  color: var(--theme-text-primary);
}

.theme-text-secondary {
  color: var(--theme-text-secondary);
}

.theme-text-tertiary {
  color: var(--theme-text-tertiary);
}

.theme-border {
  border-color: var(--theme-border-color);
}

.theme-bg-primary {
  background-color: var(--theme-bg-primary);
}

.theme-bg-secondary {
  background-color: var(--theme-bg-secondary);
}

.theme-bg-elevated {
  background-color: var(--theme-bg-elevated);
}

/* 状态指示器样式 */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
}

.status-indicator.connected {
  background-color: rgba(82, 196, 26, 0.1);
  color: var(--theme-success);
  border: 1px solid rgba(82, 196, 26, 0.2);
}

.status-indicator.disconnected {
  background-color: rgba(0, 0, 0, 0.04);
  color: var(--theme-text-tertiary);
  border: 1px solid var(--theme-border-secondary);
}

.status-indicator.error {
  background-color: rgba(255, 77, 79, 0.1);
  color: var(--theme-error);
  border: 1px solid rgba(255, 77, 79, 0.2);
}

.status-indicator.processing {
  background-color: rgba(24, 144, 255, 0.1);
  color: var(--theme-info);
  border: 1px solid rgba(24, 144, 255, 0.2);
}

/* 深色主题下的状态指示器 */
:root.dark .status-indicator.connected {
  background-color: rgba(115, 209, 61, 0.15);
  border-color: rgba(115, 209, 61, 0.3);
}

:root.dark .status-indicator.error {
  background-color: rgba(255, 120, 117, 0.15);
  border-color: rgba(255, 120, 117, 0.3);
}

:root.dark .status-indicator.processing {
  background-color: rgba(64, 169, 255, 0.15);
  border-color: rgba(64, 169, 255, 0.3);
}

/* 快捷键帮助样式 */
.shortcuts-help-modal .ant-modal-body {
  padding: 24px;
}

.shortcuts-help-modal .shortcut-key {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 11px;
  padding: 2px 6px;
  background-color: var(--theme-bg-secondary);
  border: 1px solid var(--theme-border-color);
  border-radius: 3px;
  color: var(--theme-text-primary);
}

/* 帮助文档样式 */
.help-documentation-modal .ant-modal-body {
  padding: 0;
}

.help-documentation-modal .ant-menu {
  background-color: var(--theme-bg-primary);
  border-right: 1px solid var(--theme-border-color);
}

.help-documentation-modal .ant-menu-item {
  color: var(--theme-text-primary);
}

.help-documentation-modal .ant-menu-item:hover {
  background-color: var(--theme-bg-secondary);
}

.help-documentation-modal .ant-menu-item-selected {
  background-color: rgba(24, 144, 255, 0.1);
  color: var(--theme-info);
}

/* 主题切换按钮样式 */
.theme-toggle-dropdown .ant-dropdown-menu {
  background-color: var(--theme-bg-elevated);
  border: 1px solid var(--theme-border-color);
  box-shadow: var(--theme-shadow-lg);
}

.theme-toggle-dropdown .ant-dropdown-menu-item {
  color: var(--theme-text-primary);
}

.theme-toggle-dropdown .ant-dropdown-menu-item:hover {
  background-color: var(--theme-bg-secondary);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .shortcuts-help-modal,
  .help-documentation-modal {
    width: 95% !important;
    max-width: none !important;
  }
  
  .help-documentation-modal .ant-modal-body {
    flex-direction: column;
  }
  
  .help-documentation-modal .ant-menu {
    border-right: none;
    border-bottom: 1px solid var(--theme-border-color);
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--theme-bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--theme-border-color);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--theme-text-tertiary);
}

/* 深色主题下的滚动条 */
:root.dark ::-webkit-scrollbar-track {
  background: var(--theme-bg-primary);
}

:root.dark ::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
}

:root.dark ::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}
