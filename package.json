{"name": "redis-client-solo", "version": "1.0.0", "description": "现代化Redis可视化管理工具 - 用户体验优先的设计", "type": "module", "scripts": {"dev": "concurrently \"npm run dev:server\" \"npm run dev:frontend\" --names \"SERVER,FRONTEND\" --prefix-colors \"blue,green\"", "dev:frontend": "cd frontend && npm run dev -- --host", "dev:server": "cd server && npm run dev", "dev:backend": "npm run dev:server", "build": "npm run build:frontend && npm run build:server", "build:frontend": "cd frontend && npm run build", "build:server": "cd server && npm run build", "build:backend": "npm run build:server", "install:all": "npm install && cd frontend && npm install && cd ../server && npm install && cd ../shared && npm install", "start": "concurrently \"npm run start:server\" \"npm run start:frontend\" --names \"SERVER,FRONTEND\" --prefix-colors \"blue,green\"", "start:server": "cd server && npm run start", "start:frontend": "cd frontend && npm run preview", "lint": "npm run lint:frontend && npm run lint:server", "lint:frontend": "cd frontend && npm run lint", "lint:server": "cd server && npm run lint", "lint:backend": "npm run lint:server", "test": "npm run test:frontend && npm run test:server", "test:frontend": "cd frontend && npm run test", "test:server": "cd server && npm run test", "test:backend": "npm run test:server"}, "keywords": ["redis", "database", "visualization", "management", "modern-ui", "user-experience"], "author": "Redis Client Solo Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2", "@types/node": "^20.10.0", "typescript": "^5.3.0"}, "workspaces": ["frontend", "backend", "shared"]}