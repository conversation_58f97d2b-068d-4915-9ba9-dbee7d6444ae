import { Router, Request, Response } from 'express';
import { connectionManager } from '../services/RedisConnectionManager';
import { ApiResponse } from '../types/api';
import { asyncHandler } from '../utils/asyncHandler';

const router = Router();

// Health check endpoint
router.get('/health', asyncHandler(async (req: Request, res: Response) => {
  const activeConnections = connectionManager.getActiveConnections();
  
  const response: ApiResponse = {
    success: true,
    data: {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      activeConnections: activeConnections.length,
      environment: process.env.NODE_ENV || 'development',
      version: process.env.npm_package_version || '1.0.0'
    },
    message: 'Server is running'
  };
  
  res.json(response);
}));

// Detailed health check with Redis connections status
router.get('/health/detailed', asyncHandler(async (req: Request, res: Response) => {
  const activeConnections = connectionManager.getActiveConnections();
  const connectionStatuses = activeConnections.map((conn) => {
    return {
      connectionId: conn.id,
      connected: conn.status === 'connected',
      database: conn.config.database,
      host: conn.config.host,
      port: conn.config.port,
      status: conn.status,
    };
  });

  const response: ApiResponse = {
    success: true,
    data: {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      activeConnections: activeConnections.length,
      connections: connectionStatuses,
      environment: process.env.NODE_ENV || 'development',
      version: process.env.npm_package_version || '1.0.0'
    },
    message: 'Server is running with detailed status'
  };

  res.json(response);
}));

export default router;