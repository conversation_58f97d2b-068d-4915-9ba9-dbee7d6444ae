"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const RedisConnectionManager_1 = require("../services/RedisConnectionManager");
const asyncHandler_1 = require("../utils/asyncHandler");
const router = (0, express_1.Router)();
router.get('/redis/:connectionId/info', (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    const { connectionId } = req.params;
    const client = RedisConnectionManager_1.connectionManager.getConnection(connectionId);
    if (!client) {
        return res.status(404).json({
            success: false,
            error: 'Connection not found',
        });
    }
    try {
        const info = await client.info();
        const parsedInfo = parseRedisInfo(info);
        const response = {
            success: true,
            data: parsedInfo,
            message: 'Redis info retrieved successfully'
        };
        return res.json(response);
    }
    catch (error) {
        const response = {
            success: false,
            error: error.message,
            message: 'Failed to get Redis info'
        };
        return res.status(500).json(response);
    }
}));
router.get('/redis/:connectionId/databases', (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    const { connectionId } = req.params;
    const client = RedisConnectionManager_1.connectionManager.getConnection(connectionId);
    if (!client) {
        return res.status(404).json({
            success: false,
            error: 'Connection not found',
        });
    }
    try {
        const info = await client.info('keyspace');
        const databases = parseKeyspaceInfo(info);
        const response = {
            success: true,
            data: databases,
            message: 'Databases retrieved successfully'
        };
        return res.json(response);
    }
    catch (error) {
        const response = {
            success: false,
            error: error.message,
            message: 'Failed to get databases'
        };
        return res.status(500).json(response);
    }
}));
router.get('/redis/:connectionId/keys', (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    const { connectionId } = req.params;
    const { pattern = '*', cursor = '0', count = '100', db = '0' } = req.query;
    const client = RedisConnectionManager_1.connectionManager.getConnection(connectionId);
    if (!client) {
        return res.status(404).json({
            success: false,
            error: 'Connection not found',
        });
    }
    try {
        await client.select(parseInt(db));
        const result = await client.scan(cursor, 'MATCH', pattern, 'COUNT', parseInt(count));
        const [nextCursor, keys] = result;
        const keyInfos = await Promise.all(keys.map(async (key) => {
            try {
                const [type, ttl, size] = await Promise.all([
                    client.type(key),
                    client.ttl(key),
                    getKeySize(client, key)
                ]);
                return {
                    key,
                    type,
                    ttl: ttl === -1 ? null : ttl,
                    size,
                };
            }
            catch (error) {
                return {
                    key,
                    type: 'unknown',
                    ttl: null,
                    size: 0,
                    error: error.message,
                };
            }
        }));
        const response = {
            success: true,
            data: {
                cursor: nextCursor,
                keys: keyInfos,
                hasMore: nextCursor !== '0',
            },
            message: 'Keys retrieved successfully'
        };
        return res.json(response);
    }
    catch (error) {
        const response = {
            success: false,
            error: error.message,
            message: 'Failed to get keys'
        };
        return res.status(500).json(response);
    }
}));
router.get('/redis/:connectionId/key/:key', (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    const { connectionId, key } = req.params;
    const { db = '0' } = req.query;
    const client = RedisConnectionManager_1.connectionManager.getConnection(connectionId);
    if (!client) {
        return res.status(404).json({
            success: false,
            error: 'Connection not found',
        });
    }
    try {
        await client.select(parseInt(db));
        const exists = await client.exists(key);
        if (!exists) {
            return res.status(404).json({
                success: false,
                error: 'Key not found',
            });
        }
        const [type, ttl] = await Promise.all([
            client.type(key),
            client.ttl(key)
        ]);
        let value;
        switch (type) {
            case 'string':
                value = await client.get(key);
                break;
            case 'hash':
                value = await client.hgetall(key);
                break;
            case 'list':
                value = await client.lrange(key, 0, -1);
                break;
            case 'set':
                value = await client.smembers(key);
                break;
            case 'zset':
                value = await client.zrange(key, 0, -1, 'WITHSCORES');
                break;
            default:
                value = null;
        }
        const response = {
            success: true,
            data: {
                key,
                type,
                value,
                ttl: ttl === -1 ? null : ttl,
            },
            message: 'Key value retrieved successfully'
        };
        return res.json(response);
    }
    catch (error) {
        const response = {
            success: false,
            error: error.message,
            message: 'Failed to get key value'
        };
        return res.status(500).json(response);
    }
}));
router.post('/redis/:connectionId/command', (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    const { connectionId } = req.params;
    const { command, args = [], db = 0 } = req.body;
    const client = RedisConnectionManager_1.connectionManager.getConnection(connectionId);
    if (!client) {
        return res.status(404).json({
            success: false,
            error: 'Connection not found',
        });
    }
    if (!command) {
        return res.status(400).json({
            success: false,
            error: 'Command is required',
        });
    }
    try {
        await client.select(db);
        const result = await client.call(command.toUpperCase(), ...args);
        const response = {
            success: true,
            data: { result },
            message: 'Command executed successfully'
        };
        return res.json(response);
    }
    catch (error) {
        const response = {
            success: false,
            error: error.message,
            message: 'Failed to execute command'
        };
        return res.status(500).json(response);
    }
}));
function parseRedisInfo(info) {
    const result = {};
    const sections = info.split('\r\n\r\n');
    sections.forEach(section => {
        const lines = section.split('\r\n');
        let currentSection = '';
        lines.forEach(line => {
            if (line.startsWith('# ')) {
                currentSection = line.substring(2).toLowerCase();
                result[currentSection] = {};
            }
            else if (line.includes(':') && currentSection) {
                const [key, value] = line.split(':');
                result[currentSection][key] = isNaN(Number(value)) ? value : Number(value);
            }
        });
    });
    return result;
}
function parseKeyspaceInfo(info) {
    const databases = [];
    const lines = info.split('\r\n');
    lines.forEach(line => {
        if (line.startsWith('db')) {
            const match = line.match(/db(\d+):keys=(\d+),expires=(\d+)/);
            if (match) {
                databases.push({
                    db: parseInt(match[1]),
                    keys: parseInt(match[2]),
                    expires: parseInt(match[3]),
                });
            }
        }
    });
    return databases;
}
async function getKeySize(client, key) {
    try {
        const type = await client.type(key);
        switch (type) {
            case 'string':
                return await client.strlen(key);
            case 'hash':
                return await client.hlen(key);
            case 'list':
                return await client.llen(key);
            case 'set':
                return await client.scard(key);
            case 'zset':
                return await client.zcard(key);
            default:
                return 0;
        }
    }
    catch (error) {
        return 0;
    }
}
router.get('/:connectionId/stats', (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    const { connectionId } = req.params;
    const connection = RedisConnectionManager_1.connectionManager.getConnection(connectionId);
    if (!connection) {
        return res.status(404).json({
            success: false,
            error: 'Connection not found',
        });
    }
    const { client } = connection;
    try {
        const info = await client.info();
        const infoData = parseRedisInfo(info);
        let slowlog = [];
        try {
            slowlog = await client.slowlog('get', 10);
        }
        catch (error) {
            console.warn('Failed to get slowlog:', error);
        }
        let clientCount = 0;
        try {
            const clientList = await client.client('list');
            clientCount = clientList.split('\n').filter((line) => line.trim()).length;
        }
        catch (error) {
            console.warn('Failed to get client list:', error);
        }
        const stats = {
            server: {
                version: infoData.server?.redis_version || 'unknown',
                uptime: infoData.server?.uptime_in_seconds || 0,
                mode: infoData.server?.redis_mode || 'standalone',
            },
            memory: {
                used: infoData.memory?.used_memory || 0,
                used_human: infoData.memory?.used_memory_human || '0B',
                peak: infoData.memory?.used_memory_peak || 0,
                peak_human: infoData.memory?.used_memory_peak_human || '0B',
                rss: infoData.memory?.used_memory_rss || 0,
                rss_human: infoData.memory?.used_memory_rss_human || '0B',
                fragmentation_ratio: infoData.memory?.mem_fragmentation_ratio || 0,
            },
            clients: {
                connected: infoData.clients?.connected_clients || 0,
                blocked: infoData.clients?.blocked_clients || 0,
                total: clientCount,
            },
            stats: {
                total_connections_received: infoData.stats?.total_connections_received || 0,
                total_commands_processed: infoData.stats?.total_commands_processed || 0,
                instantaneous_ops_per_sec: infoData.stats?.instantaneous_ops_per_sec || 0,
                keyspace_hits: infoData.stats?.keyspace_hits || 0,
                keyspace_misses: infoData.stats?.keyspace_misses || 0,
                hit_rate: infoData.stats?.keyspace_hits && infoData.stats?.keyspace_misses
                    ? ((infoData.stats.keyspace_hits / (infoData.stats.keyspace_hits + infoData.stats.keyspace_misses)) * 100).toFixed(2)
                    : '0.00',
            },
            cpu: {
                used_cpu_sys: infoData.cpu?.used_cpu_sys || 0,
                used_cpu_user: infoData.cpu?.used_cpu_user || 0,
                used_cpu_sys_children: infoData.cpu?.used_cpu_sys_children || 0,
                used_cpu_user_children: infoData.cpu?.used_cpu_user_children || 0,
            },
            keyspace: infoData.keyspace || {},
            slowlog: Array.isArray(slowlog) ? slowlog.map((entry) => ({
                id: entry[0],
                timestamp: entry[1],
                duration: entry[2],
                command: entry[3],
            })) : [],
        };
        const response = {
            success: true,
            data: stats,
            message: 'Redis stats retrieved successfully'
        };
        return res.json(response);
    }
    catch (error) {
        const response = {
            success: false,
            error: error.message,
            message: 'Failed to get Redis stats'
        };
        return res.status(500).json(response);
    }
}));
router.get('/:connectionId/export', (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    const { connectionId } = req.params;
    const { format = 'json', pattern = '*', db = 0 } = req.query;
    const connection = RedisConnectionManager_1.connectionManager.getConnection(connectionId);
    if (!connection) {
        return res.status(404).json({
            success: false,
            error: 'Connection not found',
        });
    }
    const { client } = connection;
    try {
        await client.select(Number(db));
        const keys = await client.keys(pattern);
        const exportData = [];
        for (const key of keys) {
            try {
                const type = await client.type(key);
                const ttl = await client.ttl(key);
                let value;
                switch (type) {
                    case 'string':
                        value = await client.get(key);
                        break;
                    case 'hash':
                        value = await client.hgetall(key);
                        break;
                    case 'list':
                        value = await client.lrange(key, 0, -1);
                        break;
                    case 'set':
                        value = await client.smembers(key);
                        break;
                    case 'zset':
                        value = await client.zrange(key, 0, -1, 'WITHSCORES');
                        break;
                    default:
                        continue;
                }
                exportData.push({
                    key,
                    type,
                    value,
                    ttl: ttl === -1 ? null : ttl,
                });
            }
            catch (error) {
                console.warn(`Failed to export key ${key}:`, error);
            }
        }
        if (format === 'csv') {
            const csvHeader = 'key,type,value,ttl\n';
            const csvRows = exportData.map(item => {
                const value = typeof item.value === 'object'
                    ? JSON.stringify(item.value).replace(/"/g, '""')
                    : String(item.value).replace(/"/g, '""');
                return `"${item.key}","${item.type}","${value}","${item.ttl || ''}"`;
            }).join('\n');
            res.setHeader('Content-Type', 'text/csv');
            res.setHeader('Content-Disposition', `attachment; filename="redis-export-${Date.now()}.csv"`);
            return res.send(csvHeader + csvRows);
        }
        else {
            const jsonData = {
                metadata: {
                    exportTime: new Date().toISOString(),
                    database: Number(db),
                    pattern: pattern,
                    totalKeys: exportData.length,
                },
                data: exportData,
            };
            res.setHeader('Content-Type', 'application/json');
            res.setHeader('Content-Disposition', `attachment; filename="redis-export-${Date.now()}.json"`);
            return res.json(jsonData);
        }
    }
    catch (error) {
        const response = {
            success: false,
            error: error.message,
            message: 'Failed to export Redis data'
        };
        return res.status(500).json(response);
    }
}));
router.post('/:connectionId/import', (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    const { connectionId } = req.params;
    const { data, db = 0, overwrite = false } = req.body;
    const connection = RedisConnectionManager_1.connectionManager.getConnection(connectionId);
    if (!connection) {
        return res.status(404).json({
            success: false,
            error: 'Connection not found',
        });
    }
    const { client } = connection;
    try {
        await client.select(Number(db));
        let importData;
        if (Array.isArray(data)) {
            importData = data;
        }
        else if (data.data && Array.isArray(data.data)) {
            importData = data.data;
        }
        else {
            throw new Error('Invalid import data format');
        }
        const results = {
            total: importData.length,
            success: 0,
            failed: 0,
            skipped: 0,
            errors: [],
        };
        for (const item of importData) {
            try {
                const { key, type, value, ttl } = item;
                if (!overwrite && await client.exists(key)) {
                    results.skipped++;
                    continue;
                }
                switch (type) {
                    case 'string':
                        await client.set(key, value);
                        break;
                    case 'hash':
                        await client.hset(key, value);
                        break;
                    case 'list':
                        await client.del(key);
                        if (Array.isArray(value) && value.length > 0) {
                            await client.lpush(key, ...value.reverse());
                        }
                        break;
                    case 'set':
                        await client.del(key);
                        if (Array.isArray(value) && value.length > 0) {
                            await client.sadd(key, ...value);
                        }
                        break;
                    case 'zset':
                        await client.del(key);
                        if (Array.isArray(value) && value.length > 0) {
                            const args = [];
                            for (let i = 0; i < value.length; i += 2) {
                                args.push(value[i + 1], value[i]);
                            }
                            if (args.length > 0) {
                                await client.zadd(key, ...args);
                            }
                        }
                        break;
                    default:
                        results.errors.push(`Unsupported type ${type} for key ${key}`);
                        results.failed++;
                        continue;
                }
                if (ttl && ttl > 0) {
                    await client.expire(key, ttl);
                }
                results.success++;
            }
            catch (error) {
                results.failed++;
                results.errors.push(`Failed to import key ${item.key}: ${error.message}`);
            }
        }
        const response = {
            success: true,
            data: results,
            message: `Import completed: ${results.success} success, ${results.failed} failed, ${results.skipped} skipped`
        };
        return res.json(response);
    }
    catch (error) {
        const response = {
            success: false,
            error: error.message,
            message: 'Failed to import Redis data'
        };
        return res.status(500).json(response);
    }
}));
exports.default = router;
//# sourceMappingURL=redis.js.map