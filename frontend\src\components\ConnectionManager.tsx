import React, { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  InputNumber,
  Switch,
  Button,
  List,
  Card,
  Badge,
  Popconfirm,
  message,
  Space,
  Typography,
  Divider,
} from 'antd';
import {
  DatabaseOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  PlayCircleOutlined,
  StopOutlined,
} from '@ant-design/icons';
import { useConnectionStore } from '../stores/connectionStore';
import { RedisConnectionConfig } from '../services/api';

const { Title, Text } = Typography;

interface ConnectionManagerProps {
  visible: boolean;
  onClose: () => void;
  onConnect: (connectionId: string) => void;
}

const ConnectionManager: React.FC<ConnectionManagerProps> = ({
  visible,
  onClose,
  onConnect,
}) => {
  const [form] = Form.useForm();
  const [showForm, setShowForm] = useState(false);
  const [editingConnection, setEditingConnection] = useState<string | null>(null);
  const [testingConnection, setTestingConnection] = useState(false);

  const {
    connections,
    loading,
    error,
    fetchConnections,
    createConnection,
    testConnection,
    deleteConnection,
    clearError,
  } = useConnectionStore();

  useEffect(() => {
    if (visible) {
      fetchConnections();
    }
  }, [visible, fetchConnections]);

  useEffect(() => {
    if (error) {
      message.error(error);
      clearError();
    }
  }, [error, clearError]);

  const handleCreateConnection = () => {
    setEditingConnection(null);
    setShowForm(true);
    form.resetFields();
    form.setFieldsValue({
      port: 6379,
      database: 0,
      ssl: false,
      timeout: 5000,
      lazyConnect: true,
      maxRetriesPerRequest: 3,
    });
  };

  const handleEditConnection = (connection: any) => {
    setEditingConnection(connection.id);
    setShowForm(true);
    form.setFieldsValue(connection);
  };

  const handleTestConnection = async () => {
    try {
      const values = await form.validateFields();
      setTestingConnection(true);
      
      const isConnectable = await testConnection(values);
      if (isConnectable) {
        message.success('连接测试成功！');
      } else {
        message.error('连接测试失败！');
      }
    } catch (error) {
      console.error('Test connection error:', error);
    } finally {
      setTestingConnection(false);
    }
  };

  const handleSaveConnection = async () => {
    try {
      const values = await form.validateFields();
      const connectionId = await createConnection(values);
      
      if (connectionId) {
        message.success('连接创建成功！');
        setShowForm(false);
        form.resetFields();
      }
    } catch (error) {
      console.error('Save connection error:', error);
    }
  };

  const handleDeleteConnection = async (id: string) => {
    try {
      await deleteConnection(id);
      message.success('连接删除成功！');
    } catch (error) {
      console.error('Delete connection error:', error);
    }
  };

  const handleConnect = (connectionId: string) => {
    onConnect(connectionId);
    onClose();
  };

  const getStatusBadge = (status: string) => {
    const statusMap = {
      connected: { status: 'success' as const, text: '已连接' },
      disconnected: { status: 'default' as const, text: '未连接' },
      connecting: { status: 'processing' as const, text: '连接中' },
      error: { status: 'error' as const, text: '错误' },
    };
    
    const config = statusMap[status as keyof typeof statusMap] || statusMap.disconnected;
    return <Badge status={config.status} text={config.text} />;
  };

  return (
    <Modal
      title="连接管理"
      open={visible}
      onCancel={onClose}
      width={800}
      footer={null}
    >
      <div className="mb-4">
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleCreateConnection}
        >
          新建连接
        </Button>
      </div>

      <List
        loading={loading}
        dataSource={connections}
        renderItem={(connection) => (
          <List.Item
            actions={[
              <Button
                type="text"
                icon={<PlayCircleOutlined />}
                onClick={() => handleConnect(connection.id)}
                disabled={connection.status === 'error'}
              >
                连接
              </Button>,
              <Button
                type="text"
                icon={<EditOutlined />}
                onClick={() => handleEditConnection(connection)}
              >
                编辑
              </Button>,
              <Popconfirm
                title="确定要删除这个连接吗？"
                onConfirm={() => handleDeleteConnection(connection.id)}
                okText="确定"
                cancelText="取消"
              >
                <Button type="text" danger icon={<DeleteOutlined />}>
                  删除
                </Button>
              </Popconfirm>,
            ]}
          >
            <List.Item.Meta
              avatar={<DatabaseOutlined className="text-2xl text-blue-500" />}
              title={
                <Space>
                  <Text strong>{connection.name}</Text>
                  {getStatusBadge(connection.status)}
                </Space>
              }
              description={
                <div>
                  <Text type="secondary">
                    {connection.host}:{connection.port} (DB {connection.database})
                  </Text>
                  {connection.lastError && (
                    <div>
                      <Text type="danger" className="text-xs">
                        错误: {connection.lastError}
                      </Text>
                    </div>
                  )}
                </div>
              }
            />
          </List.Item>
        )}
      />

      <Modal
        title={editingConnection ? '编辑连接' : '新建连接'}
        open={showForm}
        onCancel={() => setShowForm(false)}
        footer={[
          <Button key="cancel" onClick={() => setShowForm(false)}>
            取消
          </Button>,
          <Button
            key="test"
            loading={testingConnection}
            onClick={handleTestConnection}
          >
            测试连接
          </Button>,
          <Button
            key="save"
            type="primary"
            loading={loading}
            onClick={handleSaveConnection}
          >
            保存
          </Button>,
        ]}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="name"
            label="连接名称"
            rules={[{ required: true, message: '请输入连接名称' }]}
          >
            <Input placeholder="例如：本地Redis" />
          </Form.Item>

          <Form.Item
            name="host"
            label="主机地址"
            rules={[{ required: true, message: '请输入主机地址' }]}
          >
            <Input placeholder="localhost" />
          </Form.Item>

          <Form.Item
            name="port"
            label="端口"
            rules={[{ required: true, message: '请输入端口' }]}
          >
            <InputNumber min={1} max={65535} className="w-full" />
          </Form.Item>

          <Form.Item name="password" label="密码">
            <Input.Password placeholder="可选" />
          </Form.Item>

          <Form.Item name="database" label="数据库">
            <InputNumber min={0} max={15} className="w-full" />
          </Form.Item>

          <Divider />

          <Form.Item name="ssl" label="SSL连接" valuePropName="checked">
            <Switch />
          </Form.Item>

          <Form.Item name="timeout" label="连接超时(ms)">
            <InputNumber min={1000} max={30000} className="w-full" />
          </Form.Item>

          <Form.Item name="lazyConnect" label="延迟连接" valuePropName="checked">
            <Switch />
          </Form.Item>

          <Form.Item name="maxRetriesPerRequest" label="最大重试次数">
            <InputNumber min={0} max={10} className="w-full" />
          </Form.Item>
        </Form>
      </Modal>
    </Modal>
  );
};

export default ConnectionManager;
