"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const RedisConnectionManager_1 = require("../services/RedisConnectionManager");
const asyncHandler_1 = require("../utils/asyncHandler");
const router = (0, express_1.Router)();
router.get('/health', (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    const activeConnections = RedisConnectionManager_1.connectionManager.getActiveConnections();
    const response = {
        success: true,
        data: {
            status: 'healthy',
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            activeConnections: activeConnections.length,
            environment: process.env.NODE_ENV || 'development',
            version: process.env.npm_package_version || '1.0.0'
        },
        message: 'Server is running'
    };
    res.json(response);
}));
router.get('/health/detailed', (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    const activeConnections = RedisConnectionManager_1.connectionManager.getActiveConnections();
    const connectionStatuses = activeConnections.map((conn) => {
        return {
            connectionId: conn.id,
            connected: conn.status === 'connected',
            database: conn.config.database,
            host: conn.config.host,
            port: conn.config.port,
            status: conn.status,
        };
    });
    const response = {
        success: true,
        data: {
            status: 'healthy',
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            activeConnections: activeConnections.length,
            connections: connectionStatuses,
            environment: process.env.NODE_ENV || 'development',
            version: process.env.npm_package_version || '1.0.0'
        },
        message: 'Server is running with detailed status'
    };
    res.json(response);
}));
exports.default = router;
//# sourceMappingURL=health.js.map