import React, { useEffect } from 'react';
import { Badge, Tooltip, Space, Typography, Button } from 'antd';
import {
  WifiOutlined,
  DisconnectOutlined,
  ReloadOutlined,
  ExclamationCircleOutlined,
  DatabaseOutlined,
} from '@ant-design/icons';
import { useWebSocketConnection, useConnectionStatus } from '../stores/websocketStore';
import { useConnectionStore } from '../stores/connectionStore';

const { Text } = Typography;

interface RealtimeStatusIndicatorProps {
  showDetails?: boolean;
  size?: 'small' | 'default';
}

const RealtimeStatusIndicator: React.FC<RealtimeStatusIndicatorProps> = ({
  showDetails = false,
  size = 'default',
}) => {
  const { isConnected, connectionError, reconnect } = useWebSocketConnection();
  const { activeConnectionId } = useConnectionStore();
  const { status, subscribe, unsubscribe } = useConnectionStatus(activeConnectionId);

  // 当活跃连接改变时，订阅新连接的状态
  useEffect(() => {
    if (activeConnectionId && isConnected) {
      subscribe(activeConnectionId);
    } else {
      unsubscribe();
    }

    return () => {
      unsubscribe();
    };
  }, [activeConnectionId, isConnected, subscribe, unsubscribe]);

  const getWebSocketStatus = () => {
    if (connectionError) {
      return {
        status: 'error' as const,
        color: 'red',
        icon: <ExclamationCircleOutlined />,
        text: 'WebSocket连接错误',
        tooltip: connectionError,
      };
    }

    if (!isConnected) {
      return {
        status: 'offline' as const,
        color: 'default',
        icon: <DisconnectOutlined />,
        text: 'WebSocket未连接',
        tooltip: '实时功能不可用',
      };
    }

    return {
      status: 'online' as const,
      color: 'green',
      icon: <WifiOutlined />,
      text: 'WebSocket已连接',
      tooltip: '实时功能正常',
    };
  };

  const getConnectionStatus = () => {
    if (!activeConnectionId) {
      return {
        status: 'none' as const,
        color: 'default',
        text: '未选择连接',
        tooltip: '请选择一个Redis连接',
      };
    }

    if (!status) {
      return {
        status: 'unknown' as const,
        color: 'processing',
        text: '检查中...',
        tooltip: '正在检查连接状态',
      };
    }

    switch (status.status) {
      case 'connected':
        return {
          status: 'connected' as const,
          color: 'success',
          text: 'Redis已连接',
          tooltip: `连接正常 - ${new Date(status.timestamp).toLocaleTimeString()}`,
        };
      case 'disconnected':
        return {
          status: 'disconnected' as const,
          color: 'default',
          text: 'Redis未连接',
          tooltip: `连接断开 - ${new Date(status.timestamp).toLocaleTimeString()}`,
        };
      case 'error':
        return {
          status: 'error' as const,
          color: 'error',
          text: 'Redis连接错误',
          tooltip: `${status.error} - ${new Date(status.timestamp).toLocaleTimeString()}`,
        };
      default:
        return {
          status: 'unknown' as const,
          color: 'processing',
          text: '状态未知',
          tooltip: '无法确定连接状态',
        };
    }
  };

  const wsStatus = getWebSocketStatus();
  const redisStatus = getConnectionStatus();

  if (!showDetails) {
    // 简单模式：只显示状态指示器
    return (
      <Space size="small">
        <Tooltip title={wsStatus.tooltip}>
          <Badge
            status={wsStatus.status === 'online' ? 'success' : wsStatus.status === 'error' ? 'error' : 'default'}
            text={size === 'small' ? undefined : wsStatus.text}
          />
        </Tooltip>
        {activeConnectionId && (
          <Tooltip title={redisStatus.tooltip}>
            <Badge
              status={redisStatus.status === 'connected' ? 'success' : 
                     redisStatus.status === 'error' ? 'error' : 
                     redisStatus.status === 'unknown' ? 'processing' : 'default'}
              text={size === 'small' ? undefined : redisStatus.text}
            />
          </Tooltip>
        )}
      </Space>
    );
  }

  // 详细模式：显示完整状态信息
  return (
    <div className="p-4 bg-gray-50 rounded-lg">
      <div className="mb-3">
        <Text strong>实时连接状态</Text>
      </div>
      
      <Space direction="vertical" size="small" className="w-full">
        {/* WebSocket状态 */}
        <div className="flex items-center justify-between">
          <Space>
            {wsStatus.icon}
            <Text>{wsStatus.text}</Text>
          </Space>
          <div className="flex items-center space-x-2">
            <Badge status={wsStatus.status === 'online' ? 'success' : wsStatus.status === 'error' ? 'error' : 'default'} />
            {!isConnected && (
              <Button
                size="small"
                icon={<ReloadOutlined />}
                onClick={reconnect}
                type="link"
              >
                重连
              </Button>
            )}
          </div>
        </div>

        {/* Redis连接状态 */}
        {activeConnectionId && (
          <div className="flex items-center justify-between">
            <Space>
              <DatabaseOutlined />
              <Text>{redisStatus.text}</Text>
            </Space>
            <Badge 
              status={redisStatus.status === 'connected' ? 'success' : 
                     redisStatus.status === 'error' ? 'error' : 
                     redisStatus.status === 'unknown' ? 'processing' : 'default'} 
            />
          </div>
        )}

        {/* 错误信息 */}
        {connectionError && (
          <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded">
            <Text type="danger" className="text-sm">
              {connectionError}
            </Text>
          </div>
        )}

        {/* 连接错误信息 */}
        {status?.status === 'error' && status.error && (
          <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded">
            <Text type="danger" className="text-sm">
              Redis错误: {status.error}
            </Text>
          </div>
        )}
      </Space>
    </div>
  );
};

export default RealtimeStatusIndicator;
