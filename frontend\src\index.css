@tailwind base;
@tailwind components;
@tailwind utilities;

/* 导入苹果设计系统 */
@import './styles/apple-design-system.css';

/* 全局样式 */
* {
  box-sizing: border-box;
}

html, body, #root {
  height: 100%;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Roboto', 'Noto Sans SC', 'PingFang SC', 'Microsoft YaHei', sans-serif;
  background-color: #f5f5f5;
  color: #212121;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 暗色主题 */
.dark body {
  background-color: #121212;
  color: #ffffff;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.dark ::-webkit-scrollbar-track {
  background: #2d2d2d;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.dark ::-webkit-scrollbar-thumb {
  background: #555;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #777;
}

/* Ant Design 组件样式覆盖 */
.ant-layout {
  background: transparent !important;
}

.ant-layout-sider {
  background: #ffffff !important;
  border-right: 1px solid #e0e0e0;
}

.dark .ant-layout-sider {
  background: #1e1e1e !important;
  border-right: 1px solid #333333;
}

.ant-menu {
  background: transparent !important;
  border-right: none !important;
}

.ant-menu-item {
  color: #757575 !important;
}

.dark .ant-menu-item {
  color: #b3b3b3 !important;
}

.ant-menu-item:hover {
  background-color: #f5f5f5 !important;
}

.dark .ant-menu-item:hover {
  background-color: #333333 !important;
}

.ant-menu-item-selected {
  background-color: #e3f2fd !important;
  color: #1976d2 !important;
}

.dark .ant-menu-item-selected {
  background-color: #1a237e !important;
  color: #90caf9 !important;
}

/* 连接标签页样式 */
.connection-tabs .ant-tabs-tab {
  position: relative;
}

.connection-tabs .ant-tabs-tab-btn {
  display: flex;
  align-items: center;
}

.connection-tabs .ant-tabs-nav {
  margin-bottom: 0;
}

.connection-tabs .ant-tabs-content-holder {
  display: none;
}

/* 工具类 */
.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 动画 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    transform: translateY(10px); 
    opacity: 0; 
  }
  to { 
    transform: translateY(0); 
    opacity: 1; 
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}
