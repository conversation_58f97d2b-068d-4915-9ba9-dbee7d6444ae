import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export type ThemeMode = 'light' | 'dark' | 'auto';

interface ThemeState {
  // 主题模式
  mode: ThemeMode;
  
  // 当前实际应用的主题（考虑系统主题）
  currentTheme: 'light' | 'dark';
  
  // 自定义颜色配置
  primaryColor: string;
  
  // 操作方法
  setMode: (mode: ThemeMode) => void;
  setPrimaryColor: (color: string) => void;
  toggleTheme: () => void;
  
  // 内部方法
  updateCurrentTheme: () => void;
}

// 检测系统主题
const getSystemTheme = (): 'light' | 'dark' => {
  if (typeof window === 'undefined') return 'light';
  return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
};

// 计算当前应该应用的主题
const calculateCurrentTheme = (mode: ThemeMode): 'light' | 'dark' => {
  if (mode === 'auto') {
    return getSystemTheme();
  }
  return mode;
};

// 应用主题到DOM
const applyThemeToDOM = (theme: 'light' | 'dark', primaryColor: string) => {
  const root = document.documentElement;
  
  // 设置主题类名
  root.classList.remove('light', 'dark');
  root.classList.add(theme);
  
  // 设置CSS变量
  if (theme === 'dark') {
    root.style.setProperty('--ant-color-bg-base', '#141414');
    root.style.setProperty('--ant-color-bg-container', '#1f1f1f');
    root.style.setProperty('--ant-color-bg-elevated', '#262626');
    root.style.setProperty('--ant-color-text-base', '#ffffff');
    root.style.setProperty('--ant-color-text', 'rgba(255, 255, 255, 0.88)');
    root.style.setProperty('--ant-color-text-secondary', 'rgba(255, 255, 255, 0.65)');
    root.style.setProperty('--ant-color-text-tertiary', 'rgba(255, 255, 255, 0.45)');
    root.style.setProperty('--ant-color-border', 'rgba(255, 255, 255, 0.15)');
    root.style.setProperty('--ant-color-border-secondary', 'rgba(255, 255, 255, 0.06)');
  } else {
    root.style.setProperty('--ant-color-bg-base', '#ffffff');
    root.style.setProperty('--ant-color-bg-container', '#ffffff');
    root.style.setProperty('--ant-color-bg-elevated', '#ffffff');
    root.style.setProperty('--ant-color-text-base', '#000000');
    root.style.setProperty('--ant-color-text', 'rgba(0, 0, 0, 0.88)');
    root.style.setProperty('--ant-color-text-secondary', 'rgba(0, 0, 0, 0.65)');
    root.style.setProperty('--ant-color-text-tertiary', 'rgba(0, 0, 0, 0.45)');
    root.style.setProperty('--ant-color-border', 'rgba(0, 0, 0, 0.15)');
    root.style.setProperty('--ant-color-border-secondary', 'rgba(0, 0, 0, 0.06)');
  }
  
  // 设置主色调
  root.style.setProperty('--ant-color-primary', primaryColor);
};

export const useThemeStore = create<ThemeState>()(
  persist(
    (set, get) => {
      // 监听系统主题变化
      const setupSystemThemeListener = () => {
        if (typeof window === 'undefined') return;
        
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        const handleChange = () => {
          const state = get();
          if (state.mode === 'auto') {
            state.updateCurrentTheme();
          }
        };
        
        mediaQuery.addEventListener('change', handleChange);
        return () => mediaQuery.removeEventListener('change', handleChange);
      };

      // 初始化时设置监听器
      let cleanup: (() => void) | undefined;
      if (typeof window !== 'undefined') {
        cleanup = setupSystemThemeListener();
      }

      return {
        // 初始状态
        mode: 'light',
        currentTheme: 'light',
        primaryColor: '#1890ff',

        // 操作方法
        setMode: (mode: ThemeMode) => {
          set((state) => {
            const newCurrentTheme = calculateCurrentTheme(mode);
            applyThemeToDOM(newCurrentTheme, state.primaryColor);
            return {
              mode,
              currentTheme: newCurrentTheme,
            };
          });
        },

        setPrimaryColor: (color: string) => {
          set((state) => {
            applyThemeToDOM(state.currentTheme, color);
            return { primaryColor: color };
          });
        },

        toggleTheme: () => {
          const state = get();
          if (state.mode === 'auto') {
            // 如果是自动模式，切换到相反的固定主题
            const systemTheme = getSystemTheme();
            const newMode: ThemeMode = systemTheme === 'dark' ? 'light' : 'dark';
            state.setMode(newMode);
          } else {
            // 在 light 和 dark 之间切换
            const newMode: ThemeMode = state.mode === 'light' ? 'dark' : 'light';
            state.setMode(newMode);
          }
        },

        updateCurrentTheme: () => {
          set((state) => {
            const newCurrentTheme = calculateCurrentTheme(state.mode);
            if (newCurrentTheme !== state.currentTheme) {
              applyThemeToDOM(newCurrentTheme, state.primaryColor);
              return { currentTheme: newCurrentTheme };
            }
            return state;
          });
        },
      };
    },
    {
      name: 'redis-client-theme',
      partialize: (state) => ({
        mode: state.mode,
        primaryColor: state.primaryColor,
      }),
      onRehydrateStorage: () => (state) => {
        // 恢复状态后应用主题
        if (state) {
          state.updateCurrentTheme();
        }
      },
    }
  )
);

// 初始化主题（在应用启动时调用）
export const initializeTheme = () => {
  const state = useThemeStore.getState();
  state.updateCurrentTheme();
};

// 便捷的hooks
export const useTheme = () => {
  const { mode, currentTheme, primaryColor, setMode, setPrimaryColor, toggleTheme } = useThemeStore();
  
  return {
    mode,
    currentTheme,
    primaryColor,
    setMode,
    setPrimaryColor,
    toggleTheme,
    isDark: currentTheme === 'dark',
    isLight: currentTheme === 'light',
  };
};

// 预设主题颜色
export const THEME_COLORS = [
  { name: '拂晓蓝', value: '#1890ff' },
  { name: '薄暮', value: '#722ed1' },
  { name: '火山', value: '#fa541c' },
  { name: '日暮', value: '#faad14' },
  { name: '明青', value: '#13c2c2' },
  { name: '极光绿', value: '#52c41a' },
  { name: '酱紫', value: '#eb2f96' },
  { name: '胭脂', value: '#f5222d' },
];
