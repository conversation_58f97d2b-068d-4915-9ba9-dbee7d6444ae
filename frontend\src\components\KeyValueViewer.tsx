import React, { useEffect, useState } from 'react';
import {
  Card,
  Typography,
  Space,
  Tag,
  Button,
  Input,
  Table,
  List,
  Descriptions,
  message,
  Spin,
} from 'antd';
import {
  ReloadOutlined,
  CopyOutlined,
  EditOutlined,
  SaveOutlined,
  CloseOutlined,
} from '@ant-design/icons';
import { useRedisStore } from '../stores/redisStore';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

interface KeyValueViewerProps {
  connectionId: string;
  keyName: string;
  database: number;
}

const KeyValueViewer: React.FC<KeyValueViewerProps> = ({
  connectionId,
  keyName,
  database,
}) => {
  const [editing, setEditing] = useState(false);
  const [editValue, setEditValue] = useState('');
  
  const {
    keyValue,
    loading,
    error,
    fetchKeyValue,
    clearError,
  } = useRedisStore();

  useEffect(() => {
    if (connectionId && keyName) {
      fetchKeyValue(connectionId, keyName);
    }
  }, [connectionId, keyName, database, fetchKeyValue]);

  useEffect(() => {
    if (error) {
      message.error(error);
      clearError();
    }
  }, [error, clearError]);

  const handleRefresh = () => {
    if (connectionId && keyName) {
      fetchKeyValue(connectionId, keyName);
    }
  };

  const handleCopy = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      message.success('已复制到剪贴板');
    });
  };

  const handleEdit = () => {
    if (keyValue) {
      setEditValue(JSON.stringify(keyValue.value, null, 2));
      setEditing(true);
    }
  };

  const handleSave = () => {
    // TODO: 实现保存功能
    message.info('保存功能待实现');
    setEditing(false);
  };

  const handleCancel = () => {
    setEditing(false);
    setEditValue('');
  };

  const formatTTL = (ttl: number | null) => {
    if (ttl === null || ttl === -1) return '永不过期';
    if (ttl === 0) return '已过期';
    
    const days = Math.floor(ttl / 86400);
    const hours = Math.floor((ttl % 86400) / 3600);
    const minutes = Math.floor((ttl % 3600) / 60);
    const seconds = ttl % 60;
    
    if (days > 0) return `${days}天${hours}小时`;
    if (hours > 0) return `${hours}小时${minutes}分钟`;
    if (minutes > 0) return `${minutes}分钟${seconds}秒`;
    return `${seconds}秒`;
  };

  const getTypeColor = (type: string) => {
    const colorMap: Record<string, string> = {
      string: 'blue',
      hash: 'green',
      list: 'orange',
      set: 'purple',
      zset: 'red',
      stream: 'cyan',
    };
    return colorMap[type] || 'default';
  };

  const renderValue = () => {
    if (!keyValue) return null;

    const { type, value } = keyValue;

    switch (type) {
      case 'string':
        return (
          <Card size="small" title="字符串值">
            {editing ? (
              <TextArea
                value={editValue}
                onChange={(e) => setEditValue(e.target.value)}
                rows={10}
                className="font-mono"
              />
            ) : (
              <Paragraph
                copyable={{ onCopy: () => handleCopy(value) }}
                className="font-mono whitespace-pre-wrap"
              >
                {value}
              </Paragraph>
            )}
          </Card>
        );

      case 'hash':
        const hashColumns = [
          { title: '字段', dataIndex: 'field', key: 'field' },
          { title: '值', dataIndex: 'value', key: 'value', ellipsis: true },
          {
            title: '操作',
            key: 'action',
            render: (_: any, record: any) => (
              <Button
                type="text"
                icon={<CopyOutlined />}
                onClick={() => handleCopy(record.value)}
              />
            ),
          },
        ];
        
        const hashData = Object.entries(value).map(([field, val], index) => ({
          key: index,
          field,
          value: val,
        }));

        return (
          <Card size="small" title="哈希表">
            <Table
              columns={hashColumns}
              dataSource={hashData}
              pagination={false}
              size="small"
            />
          </Card>
        );

      case 'list':
        return (
          <Card size="small" title="列表">
            <List
              dataSource={value}
              renderItem={(item: any, index: number) => (
                <List.Item
                  actions={[
                    <Button
                      type="text"
                      icon={<CopyOutlined />}
                      onClick={() => handleCopy(item)}
                    />,
                  ]}
                >
                  <List.Item.Meta
                    title={`[${index}]`}
                    description={
                      <Text ellipsis={{ tooltip: item }} className="font-mono">
                        {item}
                      </Text>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        );

      case 'set':
        return (
          <Card size="small" title="集合">
            <List
              dataSource={value}
              renderItem={(item: any) => (
                <List.Item
                  actions={[
                    <Button
                      type="text"
                      icon={<CopyOutlined />}
                      onClick={() => handleCopy(item)}
                    />,
                  ]}
                >
                  <Text ellipsis={{ tooltip: item }} className="font-mono">
                    {item}
                  </Text>
                </List.Item>
              )}
            />
          </Card>
        );

      case 'zset':
        const zsetColumns = [
          { title: '成员', dataIndex: 'member', key: 'member', ellipsis: true },
          { title: '分数', dataIndex: 'score', key: 'score' },
          {
            title: '操作',
            key: 'action',
            render: (_: any, record: any) => (
              <Button
                type="text"
                icon={<CopyOutlined />}
                onClick={() => handleCopy(record.member)}
              />
            ),
          },
        ];
        
        const zsetData = [];
        for (let i = 0; i < value.length; i += 2) {
          zsetData.push({
            key: i / 2,
            member: value[i],
            score: value[i + 1],
          });
        }

        return (
          <Card size="small" title="有序集合">
            <Table
              columns={zsetColumns}
              dataSource={zsetData}
              pagination={false}
              size="small"
            />
          </Card>
        );

      default:
        return (
          <Card size="small" title="原始值">
            <Paragraph
              copyable={{ onCopy: () => handleCopy(JSON.stringify(value)) }}
              className="font-mono"
            >
              {JSON.stringify(value, null, 2)}
            </Paragraph>
          </Card>
        );
    }
  };

  return (
    <div className="p-4 h-full overflow-auto">
      <Spin spinning={loading}>
        <div className="mb-4">
          <div className="flex items-center justify-between mb-4">
            <Space>
              <Title level={4} className="mb-0">
                {keyName}
              </Title>
              {keyValue && (
                <Tag color={getTypeColor(keyValue.type)}>
                  {keyValue.type.toUpperCase()}
                </Tag>
              )}
            </Space>
            
            <Space>
              {editing ? (
                <>
                  <Button
                    type="primary"
                    icon={<SaveOutlined />}
                    onClick={handleSave}
                  >
                    保存
                  </Button>
                  <Button
                    icon={<CloseOutlined />}
                    onClick={handleCancel}
                  >
                    取消
                  </Button>
                </>
              ) : (
                <>
                  <Button
                    icon={<EditOutlined />}
                    onClick={handleEdit}
                    disabled={!keyValue}
                  >
                    编辑
                  </Button>
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={handleRefresh}
                  >
                    刷新
                  </Button>
                </>
              )}
            </Space>
          </div>

          {keyValue && (
            <Descriptions size="small" column={3}>
              <Descriptions.Item label="类型">
                <Tag color={getTypeColor(keyValue.type)}>
                  {keyValue.type.toUpperCase()}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="TTL">
                {formatTTL(keyValue.ttl)}
              </Descriptions.Item>
              <Descriptions.Item label="键名">
                <Text copyable={{ onCopy: () => handleCopy(keyValue.key) }}>
                  {keyValue.key}
                </Text>
              </Descriptions.Item>
            </Descriptions>
          )}
        </div>

        {renderValue()}
      </Spin>
    </div>
  );
};

export default KeyValueViewer;
